"""
市场数据相关数据库模型
"""

import uuid
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, ForeignKey, Index, Integer, 
    Numeric, String, Text
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


# 枚举定义
class DataFrequency(str, Enum):
    TICK = "tick"
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAILY = "1d"
    WEEKLY = "1w"
    MONTHLY = "1M"


class MarketDataType(str, Enum):
    KLINE = "kline"
    TICK = "tick"
    DEPTH = "depth"
    TRADE = "trade"


class InstrumentType(str, Enum):
    STOCK = "stock"
    FUTURE = "future"
    OPTION = "option"
    FOREX = "forex"
    CRYPTO = "crypto"
    INDEX = "index"
    FUND = "fund"


class Instrument(Base):
    """交易品种表"""
    __tablename__ = "instruments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 品种基础信息
    symbol = Column(String(20), unique=True, nullable=False, index=True, comment="品种代码")
    name = Column(String(100), nullable=False, comment="品种名称")
    name_en = Column(String(100), nullable=True, comment="英文名称")
    
    # 分类信息
    instrument_type = Column(SQLEnum(InstrumentType), nullable=False, index=True, comment="品种类型")
    exchange = Column(String(10), nullable=False, index=True, comment="交易所")
    sector = Column(String(50), nullable=True, comment="行业板块")
    industry = Column(String(50), nullable=True, comment="细分行业")
    
    # 交易信息
    currency = Column(String(3), default='CNY', comment="计价货币")
    lot_size = Column(Integer, default=1, comment="每手数量")
    tick_size = Column(Numeric(15, 8), nullable=True, comment="最小价格变动")
    multiplier = Column(Numeric(15, 4), default=1, comment="合约乘数")
    
    # 价格信息
    current_price = Column(Numeric(15, 4), nullable=True, comment="当前价格")
    pre_close = Column(Numeric(15, 4), nullable=True, comment="昨收价")
    upper_limit = Column(Numeric(15, 4), nullable=True, comment="涨停价")
    lower_limit = Column(Numeric(15, 4), nullable=True, comment="跌停价")
    
    # 市场信息
    market_cap = Column(Numeric(15, 2), nullable=True, comment="总市值")
    circulating_cap = Column(Numeric(15, 2), nullable=True, comment="流通市值")
    pe_ratio = Column(Numeric(8, 2), nullable=True, comment="市盈率")
    pb_ratio = Column(Numeric(8, 2), nullable=True, comment="市净率")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否活跃")
    is_suspended = Column(Boolean, default=False, comment="是否停牌")
    list_date = Column(DateTime, nullable=True, comment="上市日期")
    delist_date = Column(DateTime, nullable=True, comment="退市日期")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    klines = relationship("MarketKline", back_populates="instrument", cascade="all, delete-orphan")
    ticks = relationship("MarketTick", back_populates="instrument", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_instruments_symbol', 'symbol'),
        Index('idx_instruments_type', 'instrument_type'),
        Index('idx_instruments_exchange', 'exchange'),
        Index('idx_instruments_active', 'is_active'),
    )


class MarketKline(Base):
    """K线数据表"""
    __tablename__ = "market_klines"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    instrument_id = Column(UUID(as_uuid=True), ForeignKey('instruments.id'), nullable=False, index=True)
    
    # 基础信息
    symbol = Column(String(20), nullable=False, index=True, comment="品种代码")
    exchange = Column(String(10), nullable=False, comment="交易所")
    frequency = Column(SQLEnum(DataFrequency), nullable=False, index=True, comment="数据频率")
    
    # 时间信息
    timestamp = Column(DateTime, nullable=False, index=True, comment="时间戳")
    trading_date = Column(DateTime, nullable=False, index=True, comment="交易日期")
    
    # OHLCV数据
    open_price = Column(Numeric(15, 4), nullable=False, comment="开盘价")
    high_price = Column(Numeric(15, 4), nullable=False, comment="最高价")
    low_price = Column(Numeric(15, 4), nullable=False, comment="最低价")
    close_price = Column(Numeric(15, 4), nullable=False, comment="收盘价")
    volume = Column(Numeric(20, 2), default=0, comment="成交量")
    turnover = Column(Numeric(20, 2), default=0, comment="成交额")
    
    # 其他信息
    vwap = Column(Numeric(15, 4), nullable=True, comment="成交量加权平均价")
    change = Column(Numeric(15, 4), nullable=True, comment="价格变动")
    change_pct = Column(Numeric(8, 4), nullable=True, comment="变动百分比")
    
    # 技术指标（可选）
    ma5 = Column(Numeric(15, 4), nullable=True, comment="5日均线")
    ma10 = Column(Numeric(15, 4), nullable=True, comment="10日均线")
    ma20 = Column(Numeric(15, 4), nullable=True, comment="20日均线")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    instrument = relationship("Instrument", back_populates="klines")

    # 索引
    __table_args__ = (
        Index('idx_market_klines_instrument_id', 'instrument_id'),
        Index('idx_market_klines_symbol', 'symbol'),
        Index('idx_market_klines_timestamp', 'timestamp'),
        Index('idx_market_klines_trading_date', 'trading_date'),
        Index('idx_market_klines_frequency', 'frequency'),
        # 复合索引优化查询性能
        Index('idx_market_klines_symbol_freq_time', 'symbol', 'frequency', 'timestamp'),
        Index('idx_market_klines_instrument_freq_time', 'instrument_id', 'frequency', 'timestamp'),
        # 唯一约束
        Index('idx_market_klines_unique', 'symbol', 'frequency', 'timestamp', unique=True),
    )


class MarketTick(Base):
    """Tick数据表"""
    __tablename__ = "market_ticks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    instrument_id = Column(UUID(as_uuid=True), ForeignKey('instruments.id'), nullable=False, index=True)
    
    # 基础信息
    symbol = Column(String(20), nullable=False, index=True, comment="品种代码")
    exchange = Column(String(10), nullable=False, comment="交易所")
    
    # 时间信息
    timestamp = Column(DateTime, nullable=False, index=True, comment="时间戳")
    trading_date = Column(DateTime, nullable=False, index=True, comment="交易日期")
    
    # 价格信息
    last_price = Column(Numeric(15, 4), nullable=False, comment="最新价")
    pre_close = Column(Numeric(15, 4), nullable=True, comment="昨收价")
    open_price = Column(Numeric(15, 4), nullable=True, comment="开盘价")
    high_price = Column(Numeric(15, 4), nullable=True, comment="最高价")
    low_price = Column(Numeric(15, 4), nullable=True, comment="最低价")
    
    # 成交信息
    volume = Column(Numeric(20, 2), default=0, comment="成交量")
    turnover = Column(Numeric(20, 2), default=0, comment="成交额")
    
    # 买卖盘信息
    bid_price_1 = Column(Numeric(15, 4), nullable=True, comment="买1价")
    bid_volume_1 = Column(Numeric(15, 2), nullable=True, comment="买1量")
    ask_price_1 = Column(Numeric(15, 4), nullable=True, comment="卖1价")
    ask_volume_1 = Column(Numeric(15, 2), nullable=True, comment="卖1量")
    
    bid_price_2 = Column(Numeric(15, 4), nullable=True, comment="买2价")
    bid_volume_2 = Column(Numeric(15, 2), nullable=True, comment="买2量")
    ask_price_2 = Column(Numeric(15, 4), nullable=True, comment="卖2价")
    ask_volume_2 = Column(Numeric(15, 2), nullable=True, comment="卖2量")
    
    bid_price_3 = Column(Numeric(15, 4), nullable=True, comment="买3价")
    bid_volume_3 = Column(Numeric(15, 2), nullable=True, comment="买3量")
    ask_price_3 = Column(Numeric(15, 4), nullable=True, comment="卖3价")
    ask_volume_3 = Column(Numeric(15, 2), nullable=True, comment="卖3量")
    
    bid_price_4 = Column(Numeric(15, 4), nullable=True, comment="买4价")
    bid_volume_4 = Column(Numeric(15, 2), nullable=True, comment="买4量")
    ask_price_4 = Column(Numeric(15, 4), nullable=True, comment="卖4价")
    ask_volume_4 = Column(Numeric(15, 2), nullable=True, comment="卖4量")
    
    bid_price_5 = Column(Numeric(15, 4), nullable=True, comment="买5价")
    bid_volume_5 = Column(Numeric(15, 2), nullable=True, comment="买5量")
    ask_price_5 = Column(Numeric(15, 4), nullable=True, comment="卖5价")
    ask_volume_5 = Column(Numeric(15, 2), nullable=True, comment="卖5量")
    
    # 其他信息
    upper_limit = Column(Numeric(15, 4), nullable=True, comment="涨停价")
    lower_limit = Column(Numeric(15, 4), nullable=True, comment="跌停价")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    # 关系
    instrument = relationship("Instrument", back_populates="ticks")

    # 索引
    __table_args__ = (
        Index('idx_market_ticks_instrument_id', 'instrument_id'),
        Index('idx_market_ticks_symbol', 'symbol'),
        Index('idx_market_ticks_timestamp', 'timestamp'),
        Index('idx_market_ticks_trading_date', 'trading_date'),
        Index('idx_market_ticks_symbol_time', 'symbol', 'timestamp'),
        Index('idx_market_ticks_instrument_time', 'instrument_id', 'timestamp'),
    )


class MarketDepth(Base):
    """市场深度数据表"""
    __tablename__ = "market_depth"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 基础信息
    symbol = Column(String(20), nullable=False, index=True, comment="品种代码")
    exchange = Column(String(10), nullable=False, comment="交易所")
    
    # 时间信息
    timestamp = Column(DateTime, nullable=False, index=True, comment="时间戳")
    
    # 深度数据（JSON格式存储）
    bids = Column(Text, nullable=True, comment="买盘深度数据(JSON)")
    asks = Column(Text, nullable=True, comment="卖盘深度数据(JSON)")
    
    # 统计信息
    bid_count = Column(Integer, default=0, comment="买盘档数")
    ask_count = Column(Integer, default=0, comment="卖盘档数")
    total_bid_volume = Column(Numeric(20, 2), default=0, comment="总买量")
    total_ask_volume = Column(Numeric(20, 2), default=0, comment="总卖量")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")

    # 索引
    __table_args__ = (
        Index('idx_market_depth_symbol', 'symbol'),
        Index('idx_market_depth_timestamp', 'timestamp'),
        Index('idx_market_depth_symbol_time', 'symbol', 'timestamp'),
    )