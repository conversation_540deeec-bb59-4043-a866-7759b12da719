<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 静态测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .nav-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .nav-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .nav-link {
            display: block;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            text-decoration: none;
            color: white;
            text-align: center;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-section h3 {
            margin-bottom: 15px;
            color: #ffd700;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .test-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }
        
        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.3);
            border: 1px solid rgba(33, 150, 243, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 量化投资平台</h1>
            <p>静态测试页面 - 用于Puppeteer深度测试</p>
        </div>
        
        <div class="nav-container">
            <h2>📍 主要导航</h2>
            <div class="nav-links">
                <a href="/dashboard" class="nav-link" data-testid="nav-dashboard">
                    📊 仪表盘
                </a>
                <a href="/market" class="nav-link" data-testid="nav-market">
                    📈 市场行情
                </a>
                <a href="/trading" class="nav-link" data-testid="nav-trading">
                    💹 交易中心
                </a>
                <a href="/portfolio" class="nav-link" data-testid="nav-portfolio">
                    💼 投资组合
                </a>
                <a href="/strategy" class="nav-link" data-testid="nav-strategy">
                    🧠 策略中心
                </a>
                <a href="/nav-test" class="nav-link" data-testid="nav-test">
                    🧪 导航测试
                </a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 搜索功能测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <input type="text" class="search-box" placeholder="搜索股票、策略或功能..." id="search-input">
                    <button class="btn" onclick="performSearch()">搜索</button>
                </div>
                <div class="test-item">
                    <div id="search-result" class="status info">
                        等待搜索...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🖱️ 交互元素测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>按钮测试</h4>
                    <button class="btn" onclick="testButton('primary')" data-testid="btn-primary">主要按钮</button>
                    <button class="btn" onclick="testButton('secondary')" data-testid="btn-secondary">次要按钮</button>
                    <button class="btn" onclick="testButton('danger')" data-testid="btn-danger">危险按钮</button>
                </div>
                <div class="test-item">
                    <h4>表单测试</h4>
                    <input type="text" class="search-box" placeholder="测试输入框" id="test-input">
                    <button class="btn" onclick="testForm()">提交表单</button>
                </div>
                <div class="test-item">
                    <h4>状态显示</h4>
                    <div id="interaction-status" class="status info">
                        等待交互...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 API测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>后端连接测试</h4>
                    <button class="btn" onclick="testAPI('health')" data-testid="btn-api-health">健康检查</button>
                    <button class="btn" onclick="testAPI('market')" data-testid="btn-api-market">市场数据</button>
                    <button class="btn" onclick="testAPI('storage')" data-testid="btn-api-storage">存储状态</button>
                </div>
                <div class="test-item">
                    <h4>API响应</h4>
                    <div id="api-status" class="status info">
                        等待API测试...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📱 响应式测试</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4>当前视口</h4>
                    <div id="viewport-info" class="status info">
                        检测中...
                    </div>
                </div>
                <div class="test-item">
                    <h4>设备信息</h4>
                    <div id="device-info" class="status info">
                        检测中...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 搜索功能
        function performSearch() {
            const input = document.getElementById('search-input');
            const result = document.getElementById('search-result');
            const query = input.value.trim();
            
            if (query) {
                result.className = 'status success';
                result.textContent = `搜索结果: "${query}" - 功能正常`;
            } else {
                result.className = 'status error';
                result.textContent = '请输入搜索内容';
            }
        }
        
        // 按钮测试
        function testButton(type) {
            const status = document.getElementById('interaction-status');
            status.className = 'status success';
            status.textContent = `${type}按钮点击成功 - ${new Date().toLocaleTimeString()}`;
        }
        
        // 表单测试
        function testForm() {
            const input = document.getElementById('test-input');
            const status = document.getElementById('interaction-status');
            const value = input.value.trim();
            
            if (value) {
                status.className = 'status success';
                status.textContent = `表单提交成功: "${value}"`;
                input.value = '';
            } else {
                status.className = 'status error';
                status.textContent = '请填写表单内容';
            }
        }
        
        // API测试
        async function testAPI(type) {
            const status = document.getElementById('api-status');
            status.className = 'status info';
            status.textContent = `正在测试${type} API...`;
            
            const endpoints = {
                health: 'http://localhost:8000/health',
                market: 'http://localhost:8000/api/v1/market/stocks',
                storage: 'http://localhost:8000/api/v1/storage/stats'
            };
            
            try {
                const startTime = Date.now();
                const response = await fetch(endpoints[type]);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    status.className = 'status success';
                    status.textContent = `${type} API成功 (${response.status}) - ${responseTime}ms`;
                } else {
                    status.className = 'status error';
                    status.textContent = `${type} API失败 (${response.status})`;
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = `${type} API错误: ${error.message}`;
            }
        }
        
        // 更新视口信息
        function updateViewportInfo() {
            const viewportInfo = document.getElementById('viewport-info');
            const deviceInfo = document.getElementById('device-info');
            
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            viewportInfo.textContent = `视口大小: ${width} x ${height}px`;
            
            let deviceType = '桌面';
            if (width <= 768) {
                deviceType = '手机';
            } else if (width <= 1024) {
                deviceType = '平板';
            }
            
            deviceInfo.textContent = `设备类型: ${deviceType} | 用户代理: ${navigator.userAgent.substring(0, 50)}...`;
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateViewportInfo();
            
            // 监听窗口大小变化
            window.addEventListener('resize', updateViewportInfo);
            
            // 自动测试一个API
            setTimeout(() => {
                testAPI('health');
            }, 1000);
        });
        
        // 回车键搜索
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    </script>
</body>
</html>
