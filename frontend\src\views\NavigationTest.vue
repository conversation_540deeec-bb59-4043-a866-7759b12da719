<template>
  <div class="navigation-test">
    <div class="test-container">
      <h1>🧪 导航功能测试</h1>
      
      <div class="test-section">
        <h2>📍 当前路由信息</h2>
        <div class="info-card">
          <p><strong>当前路径:</strong> {{ $route.path }}</p>
          <p><strong>路由名称:</strong> {{ $route.name }}</p>
          <p><strong>页面标题:</strong> {{ $route.meta?.title || '未设置' }}</p>
        </div>
      </div>

      <div class="test-section">
        <h2>🔗 导航链接测试</h2>
        <div class="nav-links">
          <router-link to="/dashboard" class="nav-link">
            <el-button type="primary">仪表盘</el-button>
          </router-link>
          <router-link to="/market" class="nav-link">
            <el-button type="success">市场行情</el-button>
          </router-link>
          <router-link to="/trading" class="nav-link">
            <el-button type="warning">交易中心</el-button>
          </router-link>
          <router-link to="/portfolio" class="nav-link">
            <el-button type="info">投资组合</el-button>
          </router-link>
          <router-link to="/strategy" class="nav-link">
            <el-button type="danger">策略中心</el-button>
          </router-link>
        </div>
      </div>

      <div class="test-section">
        <h2>⚡ 编程式导航测试</h2>
        <div class="programmatic-nav">
          <el-button @click="navigateTo('/dashboard')" type="primary">
            跳转到仪表盘
          </el-button>
          <el-button @click="navigateTo('/market')" type="success">
            跳转到市场
          </el-button>
          <el-button @click="goBack()" type="info">
            返回上一页
          </el-button>
          <el-button @click="goForward()" type="warning">
            前进下一页
          </el-button>
        </div>
      </div>

      <div class="test-section">
        <h2>📊 路由状态</h2>
        <div class="route-status">
          <div class="status-item">
            <span class="label">历史记录长度:</span>
            <span class="value">{{ historyLength }}</span>
          </div>
          <div class="status-item">
            <span class="label">可以返回:</span>
            <span class="value" :class="canGoBack ? 'success' : 'error'">
              {{ canGoBack ? '是' : '否' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">可以前进:</span>
            <span class="value" :class="canGoForward ? 'success' : 'error'">
              {{ canGoForward ? '是' : '否' }}
            </span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h2>🔍 路由参数测试</h2>
        <div class="param-test">
          <el-input 
            v-model="testParam" 
            placeholder="输入测试参数"
            style="width: 200px; margin-right: 10px;"
          />
          <el-button @click="navigateWithParam" type="primary">
            带参数跳转
          </el-button>
        </div>
        <div v-if="$route.params.id" class="param-display">
          <p><strong>当前参数:</strong> {{ $route.params.id }}</p>
        </div>
      </div>

      <div class="test-section">
        <h2>📝 导航日志</h2>
        <div class="nav-log">
          <div v-for="(log, index) in navigationLog" :key="index" class="log-item">
            <span class="timestamp">{{ log.timestamp }}</span>
            <span class="action">{{ log.action }}</span>
            <span class="path">{{ log.path }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()

// 响应式数据
const testParam = ref('')
const navigationLog = ref<Array<{timestamp: string, action: string, path: string}>>([])
const historyLength = ref(0)

// 计算属性
const canGoBack = computed(() => historyLength.value > 1)
const canGoForward = computed(() => {
  // 这个在实际应用中比较难准确判断，这里简化处理
  return false
})

// 方法
const navigateTo = (path: string) => {
  router.push(path)
  addLog('编程式导航', path)
  ElMessage.success(`导航到: ${path}`)
}

const goBack = () => {
  if (canGoBack.value) {
    router.back()
    addLog('返回', '上一页')
    ElMessage.info('返回上一页')
  } else {
    ElMessage.warning('无法返回，已在历史记录起始位置')
  }
}

const goForward = () => {
  router.forward()
  addLog('前进', '下一页')
  ElMessage.info('前进下一页')
}

const navigateWithParam = () => {
  if (testParam.value.trim()) {
    const path = `/market/detail/${testParam.value}`
    router.push(path)
    addLog('带参数导航', path)
    ElMessage.success(`导航到: ${path}`)
  } else {
    ElMessage.warning('请输入测试参数')
  }
}

const addLog = (action: string, path: string) => {
  const timestamp = new Date().toLocaleTimeString()
  navigationLog.value.unshift({ timestamp, action, path })
  
  // 限制日志数量
  if (navigationLog.value.length > 10) {
    navigationLog.value = navigationLog.value.slice(0, 10)
  }
}

// 监听路由变化
watch(() => route.path, (newPath, oldPath) => {
  if (oldPath) {
    addLog('路由变化', `${oldPath} → ${newPath}`)
  }
  historyLength.value = window.history.length
}, { immediate: true })

// 组件挂载
onMounted(() => {
  historyLength.value = window.history.length
  addLog('页面加载', route.path)
})
</script>

<style scoped>
.navigation-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.test-section:last-child {
  border-bottom: none;
}

.test-section h2 {
  color: #409eff;
  margin-bottom: 15px;
}

.info-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.nav-links, .programmatic-nav {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.nav-link {
  text-decoration: none;
}

.route-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.label {
  font-weight: 500;
}

.value.success {
  color: #67c23a;
}

.value.error {
  color: #f56c6c;
}

.param-test {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.param-display {
  padding: 10px;
  background: #e8f4fd;
  border-radius: 4px;
  color: #409eff;
}

.nav-log {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.log-item {
  display: grid;
  grid-template-columns: 100px 120px 1fr;
  gap: 10px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.timestamp {
  color: #999;
  font-family: monospace;
}

.action {
  color: #409eff;
  font-weight: 500;
}

.path {
  color: #333;
  font-family: monospace;
}
</style>
