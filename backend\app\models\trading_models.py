"""
交易相关数据库模型
"""

import uuid
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, ForeignKey, Index, Integer, 
    Numeric, String, Text
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


# 枚举定义
class OrderStatus(str, Enum):
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIAL_FILLED = "partial_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class OrderType(str, Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(str, Enum):
    BUY = "buy"
    SELL = "sell"


class TradeType(str, Enum):
    BUY = "buy"
    SELL = "sell"


class PositionSide(str, Enum):
    LONG = "long"
    SHORT = "short"


class AccountStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    CLOSED = "closed"


class Order(Base):
    """订单表"""
    __tablename__ = "orders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    account_id = Column(UUID(as_uuid=True), ForeignKey('accounts.id'), nullable=False, index=True)
    strategy_id = Column(UUID(as_uuid=True), ForeignKey('strategies.id'), nullable=True, index=True)
    
    # 订单基础信息
    order_id = Column(String(50), unique=True, nullable=False, index=True, comment="订单ID")
    client_order_id = Column(String(50), nullable=True, comment="客户端订单ID")
    parent_order_id = Column(UUID(as_uuid=True), ForeignKey('orders.id'), nullable=True, comment="父订单ID")
    
    # 交易品种信息
    symbol = Column(String(20), nullable=False, index=True, comment="交易标的")
    exchange = Column(String(10), nullable=False, comment="交易所")
    security_type = Column(String(20), default='stock', comment="证券类型")
    
    # 订单类型和方向
    order_type = Column(SQLEnum(OrderType), nullable=False, comment="订单类型")
    side = Column(SQLEnum(OrderSide), nullable=False, comment="买卖方向")
    
    # 价格和数量
    quantity = Column(Integer, nullable=False, comment="订单数量")
    price = Column(Numeric(15, 4), nullable=True, comment="订单价格")
    stop_price = Column(Numeric(15, 4), nullable=True, comment="止损价格")
    
    # 执行信息
    filled_quantity = Column(Integer, default=0, comment="已成交数量")
    avg_fill_price = Column(Numeric(15, 4), nullable=True, comment="平均成交价格")
    remaining_quantity = Column(Integer, nullable=False, comment="剩余数量")
    
    # 状态和时间
    status = Column(SQLEnum(OrderStatus), default=OrderStatus.PENDING, nullable=False, 
                   index=True, comment="订单状态")
    submit_time = Column(DateTime, default=func.now(), nullable=False, comment="提交时间")
    fill_time = Column(DateTime, nullable=True, comment="成交时间")
    cancel_time = Column(DateTime, nullable=True, comment="撤销时间")
    
    # 手续费和成本
    commission = Column(Numeric(15, 4), default=0, comment="手续费")
    total_cost = Column(Numeric(15, 4), nullable=True, comment="总成本")
    
    # 其他信息
    time_in_force = Column(String(10), default='DAY', comment="有效期")
    reject_reason = Column(String(500), nullable=True, comment="拒绝原因")
    notes = Column(Text, nullable=True, comment="备注")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="orders")
    account = relationship("Account", back_populates="orders")
    strategy = relationship("Strategy", back_populates="orders")
    trades = relationship("Trade", back_populates="order", cascade="all, delete-orphan")
    child_orders = relationship("Order", backref="parent_order", remote_side=[id])

    # 索引
    __table_args__ = (
        Index('idx_orders_user_id', 'user_id'),
        Index('idx_orders_account_id', 'account_id'),
        Index('idx_orders_symbol', 'symbol'),
        Index('idx_orders_status', 'status'),
        Index('idx_orders_submit_time', 'submit_time'),
        Index('idx_orders_strategy_symbol', 'strategy_id', 'symbol'),
    )


class Trade(Base):
    """成交记录表"""
    __tablename__ = "trades"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    account_id = Column(UUID(as_uuid=True), ForeignKey('accounts.id'), nullable=False, index=True)
    order_id = Column(UUID(as_uuid=True), ForeignKey('orders.id'), nullable=False, index=True)
    
    # 成交基础信息
    trade_id = Column(String(50), unique=True, nullable=False, index=True, comment="成交ID")
    exchange_trade_id = Column(String(50), nullable=True, comment="交易所成交ID")
    
    # 交易品种信息
    symbol = Column(String(20), nullable=False, index=True, comment="交易标的")
    exchange = Column(String(10), nullable=False, comment="交易所")
    
    # 成交信息
    side = Column(SQLEnum(TradeType), nullable=False, comment="买卖方向")
    quantity = Column(Integer, nullable=False, comment="成交数量")
    price = Column(Numeric(15, 4), nullable=False, comment="成交价格")
    amount = Column(Numeric(15, 4), nullable=False, comment="成交金额")
    
    # 费用信息
    commission = Column(Numeric(15, 4), default=0, comment="手续费")
    stamp_tax = Column(Numeric(15, 4), default=0, comment="印花税")
    transfer_fee = Column(Numeric(15, 4), default=0, comment="过户费")
    total_cost = Column(Numeric(15, 4), nullable=False, comment="总成本")
    
    # 时间信息
    trade_time = Column(DateTime, nullable=False, comment="成交时间")
    settlement_date = Column(DateTime, nullable=True, comment="结算日期")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User")
    account = relationship("Account")
    order = relationship("Order", back_populates="trades")

    # 索引
    __table_args__ = (
        Index('idx_trades_user_id', 'user_id'),
        Index('idx_trades_account_id', 'account_id'),
        Index('idx_trades_order_id', 'order_id'),
        Index('idx_trades_symbol', 'symbol'),
        Index('idx_trades_trade_time', 'trade_time'),
    )


class Position(Base):
    """持仓表"""
    __tablename__ = "positions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    account_id = Column(UUID(as_uuid=True), ForeignKey('accounts.id'), nullable=False, index=True)
    strategy_id = Column(UUID(as_uuid=True), ForeignKey('strategies.id'), nullable=True, index=True)
    
    # 持仓基础信息
    symbol = Column(String(20), nullable=False, index=True, comment="交易标的")
    exchange = Column(String(10), nullable=False, comment="交易所")
    security_type = Column(String(20), default='stock', comment="证券类型")
    side = Column(SQLEnum(PositionSide), nullable=False, comment="持仓方向")
    
    # 持仓数量和价格
    total_quantity = Column(Integer, default=0, comment="总持仓数量")
    available_quantity = Column(Integer, default=0, comment="可用数量")
    frozen_quantity = Column(Integer, default=0, comment="冻结数量")
    
    # 成本信息
    avg_cost_price = Column(Numeric(15, 4), nullable=True, comment="平均成本价")
    total_cost = Column(Numeric(15, 4), default=0, comment="总成本")
    
    # 市值信息
    current_price = Column(Numeric(15, 4), nullable=True, comment="当前价格")
    market_value = Column(Numeric(15, 4), nullable=True, comment="市值")
    
    # 盈亏信息
    unrealized_pnl = Column(Numeric(15, 4), default=0, comment="未实现盈亏")
    realized_pnl = Column(Numeric(15, 4), default=0, comment="已实现盈亏")
    total_pnl = Column(Numeric(15, 4), default=0, comment="总盈亏")
    pnl_percentage = Column(Numeric(8, 4), default=0, comment="盈亏比例")
    
    # 风控信息
    stop_loss_price = Column(Numeric(15, 4), nullable=True, comment="止损价格")
    take_profit_price = Column(Numeric(15, 4), nullable=True, comment="止盈价格")
    
    # 时间信息
    open_date = Column(DateTime, nullable=True, comment="建仓日期")
    last_trade_date = Column(DateTime, nullable=True, comment="最后交易日期")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="positions")
    account = relationship("Account", back_populates="positions")
    strategy = relationship("Strategy", back_populates="positions")

    # 索引
    __table_args__ = (
        Index('idx_positions_user_id', 'user_id'),
        Index('idx_positions_account_id', 'account_id'),
        Index('idx_positions_symbol', 'symbol'),
        Index('idx_positions_strategy_symbol', 'strategy_id', 'symbol'),
        # 唯一约束：每个账户每个标的只能有一个持仓记录
        Index('idx_positions_account_symbol_unique', 'account_id', 'symbol', unique=True),
    )


class Account(Base):
    """账户表"""
    __tablename__ = "accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, unique=True)
    
    # 账户基础信息
    account_id = Column(String(50), unique=True, nullable=False, comment="账户ID")
    account_name = Column(String(100), nullable=False, comment="账户名称")
    account_type = Column(String(20), default='stock', comment="账户类型")
    broker = Column(String(50), nullable=True, comment="券商")
    
    # 资金信息
    total_assets = Column(Numeric(15, 2), default=0, comment="总资产")
    available_cash = Column(Numeric(15, 2), default=0, comment="可用现金")
    frozen_cash = Column(Numeric(15, 2), default=0, comment="冻结现金")
    market_value = Column(Numeric(15, 2), default=0, comment="持仓市值")
    
    # 盈亏信息
    total_profit = Column(Numeric(15, 2), default=0, comment="总盈亏")
    day_profit = Column(Numeric(15, 2), default=0, comment="当日盈亏")
    realized_profit = Column(Numeric(15, 2), default=0, comment="已实现盈亏")
    unrealized_profit = Column(Numeric(15, 2), default=0, comment="未实现盈亏")
    
    # 风控信息
    max_order_value = Column(Numeric(15, 2), nullable=True, comment="单笔最大订单金额")
    daily_loss_limit = Column(Numeric(15, 2), nullable=True, comment="日损失限制")
    position_limit = Column(Numeric(8, 4), nullable=True, comment="持仓限制比例")
    
    # 状态信息
    status = Column(SQLEnum(AccountStatus), default=AccountStatus.ACTIVE, 
                   nullable=False, comment="账户状态")
    is_paper_trading = Column(Boolean, default=True, comment="是否模拟交易")
    
    # 时间信息
    last_update_time = Column(DateTime, nullable=True, comment="最后更新时间")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="account")
    orders = relationship("Order", back_populates="account")
    positions = relationship("Position", back_populates="account")
    account_history = relationship("AccountHistory", back_populates="account", 
                                  cascade="all, delete-orphan")
    asset_records = relationship("AssetRecord", back_populates="account", 
                               cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_accounts_user_id', 'user_id'),
        Index('idx_accounts_account_id', 'account_id'),
        Index('idx_accounts_status', 'status'),
    )


class AccountHistory(Base):
    """账户历史记录表"""
    __tablename__ = "account_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    account_id = Column(UUID(as_uuid=True), ForeignKey('accounts.id'), nullable=False, index=True)
    
    # 记录信息
    record_date = Column(DateTime, nullable=False, index=True, comment="记录日期")
    
    # 资金信息快照
    total_assets = Column(Numeric(15, 2), nullable=False, comment="总资产")
    available_cash = Column(Numeric(15, 2), nullable=False, comment="可用现金")
    market_value = Column(Numeric(15, 2), nullable=False, comment="持仓市值")
    
    # 盈亏信息快照
    day_profit = Column(Numeric(15, 2), default=0, comment="当日盈亏")
    total_profit = Column(Numeric(15, 2), default=0, comment="总盈亏")
    profit_rate = Column(Numeric(8, 4), default=0, comment="收益率")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    # 关系
    account = relationship("Account", back_populates="account_history")

    # 索引
    __table_args__ = (
        Index('idx_account_history_account_id', 'account_id'),
        Index('idx_account_history_record_date', 'record_date'),
        Index('idx_account_history_account_date', 'account_id', 'record_date'),
    )


class AssetRecord(Base):
    """资产变动记录表"""
    __tablename__ = "asset_records"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    account_id = Column(UUID(as_uuid=True), ForeignKey('accounts.id'), nullable=False, index=True)
    trade_id = Column(UUID(as_uuid=True), ForeignKey('trades.id'), nullable=True, index=True)
    
    # 变动信息
    change_type = Column(String(20), nullable=False, comment="变动类型")
    amount = Column(Numeric(15, 2), nullable=False, comment="变动金额")
    balance_before = Column(Numeric(15, 2), nullable=False, comment="变动前余额")
    balance_after = Column(Numeric(15, 2), nullable=False, comment="变动后余额")
    
    # 描述信息
    description = Column(String(500), nullable=True, comment="变动描述")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    # 关系
    account = relationship("Account", back_populates="asset_records")
    trade = relationship("Trade")

    # 索引
    __table_args__ = (
        Index('idx_asset_records_account_id', 'account_id'),
        Index('idx_asset_records_created_at', 'created_at'),
        Index('idx_asset_records_change_type', 'change_type'),
    )