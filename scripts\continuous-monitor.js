#!/usr/bin/env node

/**
 * 持续监控脚本
 * 定期运行健康检查，监控项目状态变化
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import chalk from 'chalk'
import { HealthChecker } from './health-check.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.resolve(__dirname, '..')

class ContinuousMonitor {
  constructor(options = {}) {
    this.interval = options.interval || 30 * 60 * 1000 // 默认30分钟
    this.maxReports = options.maxReports || 50 // 最多保留50个报告
    this.reportsDir = path.join(projectRoot, 'monitoring-reports')
    this.alertThresholds = {
      consecutiveFailures: 3,
      warningIncrease: 5
    }
    this.state = {
      isRunning: false,
      lastCheck: null,
      consecutiveFailures: 0,
      history: []
    }
    
    this.ensureReportsDirectory()
  }

  // 确保报告目录存在
  ensureReportsDirectory() {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true })
    }
  }

  // 开始监控
  start() {
    if (this.state.isRunning) {
      console.log(chalk.yellow('⚠️  监控已在运行中'))
      return
    }

    console.log(chalk.blue('🚀 启动持续监控...'))
    console.log(chalk.gray(`   检查间隔: ${this.interval / 1000 / 60} 分钟`))
    console.log(chalk.gray(`   报告目录: ${this.reportsDir}`))
    
    this.state.isRunning = true
    this.runCheck() // 立即运行一次检查
    
    this.intervalId = setInterval(() => {
      this.runCheck()
    }, this.interval)

    // 优雅关闭处理
    process.on('SIGINT', () => this.stop())
    process.on('SIGTERM', () => this.stop())
  }

  // 停止监控
  stop() {
    if (!this.state.isRunning) {
      return
    }

    console.log(chalk.blue('\n🛑 停止持续监控...'))
    
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
    
    this.state.isRunning = false
    this.saveState()
    
    console.log(chalk.green('✅ 监控已停止'))
    process.exit(0)
  }

  // 运行健康检查
  async runCheck() {
    const timestamp = new Date().toISOString()
    console.log(chalk.blue(`\n🔍 [${timestamp}] 运行健康检查...`))

    try {
      const checker = new HealthChecker()
      await checker.runAllChecks()
      
      const result = checker.results
      this.processCheckResult(result)
      this.saveReport(result)
      this.updateHistory(result)
      this.checkAlerts(result)
      
      this.state.lastCheck = timestamp
      this.saveState()
      
    } catch (error) {
      console.error(chalk.red('❌ 健康检查失败:'), error)
      this.state.consecutiveFailures++
      this.checkAlerts({ overall: 'error', error: error.message })
    }
  }

  // 处理检查结果
  processCheckResult(result) {
    const { overall, summary } = result
    
    if (overall === 'healthy') {
      this.state.consecutiveFailures = 0
      console.log(chalk.green('✅ 项目状态健康'))
    } else if (overall === 'warning') {
      console.log(chalk.yellow(`⚠️  项目有 ${summary.warnings} 个警告`))
    } else {
      this.state.consecutiveFailures++
      console.log(chalk.red(`❌ 项目不健康，有 ${summary.failed} 个失败项`))
    }
  }

  // 保存报告
  saveReport(result) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `health-report-${timestamp}.json`
    const filepath = path.join(this.reportsDir, filename)
    
    fs.writeFileSync(filepath, JSON.stringify(result, null, 2))
    
    // 清理旧报告
    this.cleanupOldReports()
  }

  // 清理旧报告
  cleanupOldReports() {
    try {
      const files = fs.readdirSync(this.reportsDir)
        .filter(file => file.startsWith('health-report-') && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(this.reportsDir, file),
          mtime: fs.statSync(path.join(this.reportsDir, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime)

      if (files.length > this.maxReports) {
        const filesToDelete = files.slice(this.maxReports)
        for (const file of filesToDelete) {
          fs.unlinkSync(file.path)
        }
        console.log(chalk.gray(`🗑️  清理了 ${filesToDelete.length} 个旧报告`))
      }
    } catch (error) {
      console.error(chalk.yellow('⚠️  清理旧报告失败:'), error.message)
    }
  }

  // 更新历史记录
  updateHistory(result) {
    this.state.history.push({
      timestamp: new Date().toISOString(),
      overall: result.overall,
      summary: result.summary
    })

    // 只保留最近100条记录
    if (this.state.history.length > 100) {
      this.state.history = this.state.history.slice(-100)
    }
  }

  // 检查告警条件
  checkAlerts(result) {
    const alerts = []

    // 连续失败告警
    if (this.state.consecutiveFailures >= this.alertThresholds.consecutiveFailures) {
      alerts.push({
        type: 'consecutive_failures',
        message: `连续 ${this.state.consecutiveFailures} 次健康检查失败`,
        severity: 'critical'
      })
    }

    // 警告数量增加告警
    if (result.summary && result.summary.warnings >= this.alertThresholds.warningIncrease) {
      alerts.push({
        type: 'warning_increase',
        message: `警告数量达到 ${result.summary.warnings} 个`,
        severity: 'warning'
      })
    }

    // 趋势分析告警
    if (this.state.history.length >= 5) {
      const recentHistory = this.state.history.slice(-5)
      const unhealthyCount = recentHistory.filter(h => h.overall !== 'healthy').length
      
      if (unhealthyCount >= 3) {
        alerts.push({
          type: 'trend_degradation',
          message: `最近5次检查中有 ${unhealthyCount} 次不健康`,
          severity: 'warning'
        })
      }
    }

    // 发送告警
    if (alerts.length > 0) {
      this.sendAlerts(alerts)
    }
  }

  // 发送告警
  sendAlerts(alerts) {
    console.log(chalk.red('\n🚨 告警触发:'))
    
    for (const alert of alerts) {
      const color = alert.severity === 'critical' ? 'red' : 'yellow'
      const icon = alert.severity === 'critical' ? '🔥' : '⚠️'
      
      console.log(chalk[color](`${icon} [${alert.severity.toUpperCase()}] ${alert.message}`))
    }

    // 保存告警记录
    const alertLog = {
      timestamp: new Date().toISOString(),
      alerts: alerts,
      consecutiveFailures: this.state.consecutiveFailures
    }

    const alertsFile = path.join(this.reportsDir, 'alerts.jsonl')
    fs.appendFileSync(alertsFile, JSON.stringify(alertLog) + '\n')

    // 这里可以添加其他告警方式，如邮件、Slack、钉钉等
    // await this.sendEmailAlert(alerts)
    // await this.sendSlackAlert(alerts)
  }

  // 保存监控状态
  saveState() {
    const stateFile = path.join(this.reportsDir, 'monitor-state.json')
    fs.writeFileSync(stateFile, JSON.stringify(this.state, null, 2))
  }

  // 加载监控状态
  loadState() {
    const stateFile = path.join(this.reportsDir, 'monitor-state.json')
    if (fs.existsSync(stateFile)) {
      try {
        const state = JSON.parse(fs.readFileSync(stateFile, 'utf8'))
        this.state = { ...this.state, ...state }
        console.log(chalk.gray('📂 已加载监控状态'))
      } catch (error) {
        console.log(chalk.yellow('⚠️  加载监控状态失败，使用默认状态'))
      }
    }
  }

  // 生成监控报告
  generateMonitoringReport() {
    if (this.state.history.length === 0) {
      console.log(chalk.yellow('⚠️  没有历史数据，无法生成报告'))
      return
    }

    const report = {
      period: {
        start: this.state.history[0].timestamp,
        end: this.state.history[this.state.history.length - 1].timestamp,
        checks: this.state.history.length
      },
      summary: {
        healthy: this.state.history.filter(h => h.overall === 'healthy').length,
        warning: this.state.history.filter(h => h.overall === 'warning').length,
        unhealthy: this.state.history.filter(h => h.overall === 'unhealthy').length
      },
      trends: this.analyzeTrends(),
      recommendations: this.generateRecommendations()
    }

    const reportFile = path.join(this.reportsDir, 'monitoring-summary.json')
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2))

    console.log(chalk.blue('📊 监控报告已生成:'))
    console.log(chalk.gray(`   文件: ${reportFile}`))
    console.log(chalk.gray(`   检查次数: ${report.period.checks}`))
    console.log(chalk.gray(`   健康率: ${(report.summary.healthy / report.period.checks * 100).toFixed(1)}%`))

    return report
  }

  // 分析趋势
  analyzeTrends() {
    // 简单的趋势分析
    const recent = this.state.history.slice(-10)
    const healthyCount = recent.filter(h => h.overall === 'healthy').length
    
    return {
      recent_health_rate: healthyCount / recent.length,
      trend: healthyCount >= 7 ? 'improving' : healthyCount <= 3 ? 'degrading' : 'stable'
    }
  }

  // 生成建议
  generateRecommendations() {
    const recommendations = []
    
    if (this.state.consecutiveFailures > 0) {
      recommendations.push('立即检查并修复失败的检查项')
    }
    
    const trends = this.analyzeTrends()
    if (trends.trend === 'degrading') {
      recommendations.push('项目健康状况呈下降趋势，建议进行全面检查')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('项目状态良好，继续保持当前的开发实践')
    }
    
    return recommendations
  }

  // 显示状态
  showStatus() {
    console.log(chalk.blue('📊 监控状态:'))
    console.log(`   运行状态: ${this.state.isRunning ? chalk.green('运行中') : chalk.gray('已停止')}`)
    console.log(`   最后检查: ${this.state.lastCheck || '从未运行'}`)
    console.log(`   连续失败: ${this.state.consecutiveFailures}`)
    console.log(`   历史记录: ${this.state.history.length} 条`)
    
    if (this.state.history.length > 0) {
      const recent = this.state.history.slice(-5)
      const healthyCount = recent.filter(h => h.overall === 'healthy').length
      console.log(`   最近健康率: ${(healthyCount / recent.length * 100).toFixed(1)}%`)
    }
  }
}

// 命令行接口
const args = process.argv.slice(2)
const command = args[0]

const monitor = new ContinuousMonitor()
monitor.loadState()

switch (command) {
  case 'start':
    monitor.start()
    break
  case 'stop':
    monitor.stop()
    break
  case 'status':
    monitor.showStatus()
    break
  case 'report':
    monitor.generateMonitoringReport()
    break
  case 'check':
    monitor.runCheck()
    break
  default:
    console.log(chalk.blue('🔍 持续监控工具'))
    console.log('')
    console.log('用法:')
    console.log('  node continuous-monitor.js start   # 开始监控')
    console.log('  node continuous-monitor.js stop    # 停止监控')
    console.log('  node continuous-monitor.js status  # 查看状态')
    console.log('  node continuous-monitor.js report  # 生成报告')
    console.log('  node continuous-monitor.js check   # 运行单次检查')
    break
}
