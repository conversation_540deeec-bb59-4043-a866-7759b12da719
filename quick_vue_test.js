/**
 * 快速Vue应用验证脚本
 */

const puppeteer = require('puppeteer');

async function quickTest() {
    console.log('🚀 快速验证Vue应用状态...');
    
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox']
    });
    
    const page = await browser.newPage();
    
    try {
        await page.goto('http://localhost:5173', { 
            waitUntil: 'domcontentloaded',
            timeout: 15000 
        });
        
        // 等待Vue应用初始化
        await page.waitForSelector('#app', { timeout: 5000 });
        
        // 检查页面内容
        const pageInfo = await page.evaluate(() => {
            return {
                title: document.title,
                hasVueApp: !!document.querySelector('#app'),
                hasMainScript: !!document.querySelector('script[src*="main.ts"]'),
                hasViteClient: !!document.querySelector('script[src*="@vite/client"]'),
                bodyContent: document.body.innerHTML.substring(0, 500),
                appContent: document.querySelector('#app') ? 
                    document.querySelector('#app').innerHTML.substring(0, 300) : 'No #app found'
            };
        });
        
        console.log('📊 页面信息:');
        console.log(`标题: ${pageInfo.title}`);
        console.log(`Vue应用容器: ${pageInfo.hasVueApp ? '✅' : '❌'}`);
        console.log(`主脚本: ${pageInfo.hasMainScript ? '✅' : '❌'}`);
        console.log(`Vite客户端: ${pageInfo.hasViteClient ? '✅' : '❌'}`);
        
        // 检查是否是静态页面还是Vue应用
        const isStatic = pageInfo.bodyContent.includes('功能模块') && 
                         pageInfo.bodyContent.includes('feature-card');
        
        if (isStatic) {
            console.log('⚠️  检测到静态页面内容');
        } else {
            console.log('✅ 检测到Vue应用结构');
        }
        
        console.log(`应用内容预览: ${pageInfo.appContent}`);
        
        await browser.close();
        
        return pageInfo;
        
    } catch (error) {
        console.error('❌ 验证失败:', error.message);
        await browser.close();
        return null;
    }
}

quickTest();
