#!/usr/bin/env python3
"""
检查API定义
"""

import requests
import json

def check_openapi():
    """检查OpenAPI定义"""
    try:
        response = requests.get("http://localhost:8000/openapi.json", timeout=10)
        if response.status_code == 200:
            openapi_data = response.json()
            
            # 查找认证相关的路径
            paths = openapi_data.get("paths", {})
            
            print("🔍 查找认证相关的API端点...")
            print("=" * 50)
            
            for path, methods in paths.items():
                if "auth" in path.lower() or "login" in path.lower():
                    print(f"\n📍 路径: {path}")
                    for method, details in methods.items():
                        print(f"  方法: {method.upper()}")
                        print(f"  摘要: {details.get('summary', 'N/A')}")
                        
                        # 检查请求体
                        request_body = details.get('requestBody', {})
                        if request_body:
                            content = request_body.get('content', {})
                            for content_type, schema_info in content.items():
                                print(f"  请求类型: {content_type}")
                                schema = schema_info.get('schema', {})
                                if '$ref' in schema:
                                    print(f"  模式引用: {schema['$ref']}")
                                elif 'properties' in schema:
                                    print(f"  属性: {list(schema['properties'].keys())}")
                        
                        print("-" * 30)
            
            # 查找LoginRequest模式定义
            components = openapi_data.get("components", {})
            schemas = components.get("schemas", {})
            
            print("\n🔍 查找LoginRequest模式定义...")
            for schema_name, schema_def in schemas.items():
                if "login" in schema_name.lower() or "auth" in schema_name.lower():
                    print(f"\n📋 模式: {schema_name}")
                    properties = schema_def.get("properties", {})
                    required = schema_def.get("required", [])
                    print(f"  属性: {list(properties.keys())}")
                    print(f"  必需: {required}")
                    
                    for prop_name, prop_def in properties.items():
                        print(f"    {prop_name}: {prop_def.get('type', 'unknown')} - {prop_def.get('description', 'N/A')}")
        else:
            print(f"❌ 无法获取OpenAPI文档: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查API失败: {e}")

if __name__ == "__main__":
    check_openapi()
