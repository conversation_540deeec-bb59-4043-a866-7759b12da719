<template>
  <div class="simple-dashboard">
    <div class="header">
      <h1>📊 投资仪表盘</h1>
      <button @click="goBack" class="back-btn">← 返回首页</button>
    </div>
    
    <div class="dashboard-content">
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-icon">💰</div>
          <div class="metric-info">
            <h3>总资产</h3>
            <div class="metric-value">¥1,234,567</div>
            <div class="metric-change positive">+12.5%</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">📈</div>
          <div class="metric-info">
            <h3>今日收益</h3>
            <div class="metric-value">¥8,765</div>
            <div class="metric-change positive">+2.3%</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">🎯</div>
          <div class="metric-info">
            <h3>策略数量</h3>
            <div class="metric-value">15</div>
            <div class="metric-change neutral">运行中</div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="metric-icon">⚡</div>
          <div class="metric-info">
            <h3>API响应</h3>
            <div class="metric-value">{{ apiResponseTime }}ms</div>
            <div class="metric-change" :class="apiStatus">{{ apiStatusText }}</div>
          </div>
        </div>
      </div>
      
      <div class="actions-section">
        <h3>🚀 快速操作</h3>
        <div class="action-buttons">
          <button @click="testMarketAPI" class="action-btn primary">
            📊 获取市场数据
          </button>
          <button @click="testTradingAPI" class="action-btn secondary">
            💹 模拟交易
          </button>
          <button @click="testStrategyAPI" class="action-btn success">
            🧠 运行策略
          </button>
          <button @click="refreshData" class="action-btn info">
            🔄 刷新数据
          </button>
        </div>
      </div>
      
      <div class="log-section">
        <h3>📝 操作日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const apiResponseTime = ref(0)
const apiStatus = ref('checking')
const apiStatusText = ref('检测中')
const logs = ref<Array<{time: string, message: string, type: string}>>([])

const goBack = () => {
  router.push('/')
}

const addLog = (message: string, type: string = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 只保留最近20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const testAPI = async (endpoint: string, name: string) => {
  const startTime = Date.now()
  addLog(`开始测试 ${name}...`, 'info')
  
  try {
    const response = await fetch(`http://localhost:8000${endpoint}`)
    const endTime = Date.now()
    const responseTime = endTime - startTime
    
    if (response.ok) {
      addLog(`✅ ${name} 测试成功 (${responseTime}ms)`, 'success')
      return { success: true, responseTime }
    } else {
      addLog(`⚠️ ${name} 响应异常 (${response.status})`, 'warning')
      return { success: false, responseTime }
    }
  } catch (error) {
    const endTime = Date.now()
    const responseTime = endTime - startTime
    addLog(`❌ ${name} 连接失败: ${(error as Error).message}`, 'error')
    return { success: false, responseTime }
  }
}

const testMarketAPI = () => testAPI('/api/v1/market/stocks', '市场数据API')
const testTradingAPI = () => testAPI('/api/v1/trading/orders', '交易API')
const testStrategyAPI = () => testAPI('/api/v1/strategy/list', '策略API')

const refreshData = async () => {
  addLog('🔄 开始刷新数据...', 'info')
  
  const healthResult = await testAPI('/health', '健康检查')
  
  if (healthResult.success) {
    apiResponseTime.value = healthResult.responseTime
    apiStatus.value = healthResult.responseTime < 200 ? 'positive' : 'neutral'
    apiStatusText.value = healthResult.responseTime < 200 ? '优秀' : '正常'
  } else {
    apiStatus.value = 'negative'
    apiStatusText.value = '异常'
  }
  
  addLog('✅ 数据刷新完成', 'success')
}

onMounted(() => {
  addLog('📊 仪表盘已加载', 'info')
  refreshData()
})
</script>

<style scoped>
.simple-dashboard {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  color: #2c3e50;
  margin: 0;
}

.back-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #5a6268;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.metric-info h3 {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0 0 5px 0;
  text-transform: uppercase;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.metric-change {
  font-size: 0.9rem;
  font-weight: 600;
}

.metric-change.positive { color: #28a745; }
.metric-change.negative { color: #dc3545; }
.metric-change.neutral { color: #6c757d; }

.actions-section, .log-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.actions-section h3, .log-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary { background: #007bff; color: white; }
.action-btn.secondary { background: #6c757d; color: white; }
.action-btn.success { background: #28a745; color: white; }
.action-btn.info { background: #17a2b8; color: white; }

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
  background: #f8f9fa;
}

.log-item {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-family: monospace;
  color: #6c757d;
  margin-right: 15px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.success .log-message { color: #28a745; }
.log-item.error .log-message { color: #dc3545; }
.log-item.warning .log-message { color: #ffc107; }
.log-item.info .log-message { color: #17a2b8; }

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
