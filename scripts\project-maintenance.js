#!/usr/bin/env node

/**
 * 项目维护脚本
 * 自动化项目维护任务，包括依赖更新、代码清理、性能优化等
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import chalk from 'chalk'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.resolve(__dirname, '..')

class ProjectMaintenance {
  constructor() {
    this.tasks = new Map()
    this.results = []
    this.setupTasks()
  }

  // 设置维护任务
  setupTasks() {
    this.tasks.set('cleanup', {
      name: '清理项目',
      description: '清理临时文件、缓存和构建产物',
      fn: this.cleanupProject.bind(this)
    })

    this.tasks.set('deps-check', {
      name: '依赖检查',
      description: '检查过期依赖和安全漏洞',
      fn: this.checkDependencies.bind(this)
    })

    this.tasks.set('deps-update', {
      name: '依赖更新',
      description: '更新项目依赖到最新版本',
      fn: this.updateDependencies.bind(this)
    })

    this.tasks.set('code-format', {
      name: '代码格式化',
      description: '格式化所有代码文件',
      fn: this.formatCode.bind(this)
    })

    this.tasks.set('lint-fix', {
      name: '代码检查修复',
      description: '自动修复可修复的代码问题',
      fn: this.fixLinting.bind(this)
    })

    this.tasks.set('optimize', {
      name: '性能优化',
      description: '优化项目性能和构建配置',
      fn: this.optimizeProject.bind(this)
    })

    this.tasks.set('security-audit', {
      name: '安全审计',
      description: '检查安全漏洞和最佳实践',
      fn: this.securityAudit.bind(this)
    })

    this.tasks.set('docs-update', {
      name: '文档更新',
      description: '更新项目文档和README',
      fn: this.updateDocumentation.bind(this)
    })
  }

  // 运行指定任务
  async runTask(taskName) {
    const task = this.tasks.get(taskName)
    if (!task) {
      throw new Error(`未知任务: ${taskName}`)
    }

    console.log(chalk.blue(`🔧 执行任务: ${task.name}`))
    console.log(chalk.gray(`   ${task.description}`))

    const startTime = Date.now()
    try {
      const result = await task.fn()
      const duration = Date.now() - startTime
      
      this.results.push({
        task: taskName,
        name: task.name,
        status: 'success',
        duration,
        result
      })

      console.log(chalk.green(`✅ ${task.name} 完成 (${duration}ms)`))
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      
      this.results.push({
        task: taskName,
        name: task.name,
        status: 'error',
        duration,
        error: error.message
      })

      console.log(chalk.red(`❌ ${task.name} 失败: ${error.message}`))
      throw error
    }
  }

  // 运行所有任务
  async runAllTasks() {
    console.log(chalk.blue('🚀 开始项目维护...\n'))

    const taskNames = Array.from(this.tasks.keys())
    for (const taskName of taskNames) {
      try {
        await this.runTask(taskName)
      } catch (error) {
        console.log(chalk.yellow(`⚠️  跳过失败的任务: ${taskName}`))
      }
      console.log('')
    }

    this.generateReport()
  }

  // 清理项目
  async cleanupProject() {
    const cleanupActions = []

    // 清理前端
    const frontendPath = path.join(projectRoot, 'frontend')
    if (fs.existsSync(frontendPath)) {
      process.chdir(frontendPath)
      
      // 清理构建产物
      const dirsToClean = ['dist', '.vite', 'node_modules/.cache']
      for (const dir of dirsToClean) {
        if (fs.existsSync(dir)) {
          fs.rmSync(dir, { recursive: true, force: true })
          cleanupActions.push(`删除 frontend/${dir}`)
        }
      }

      // 清理日志文件
      const logFiles = fs.readdirSync('.').filter(file => file.endsWith('.log'))
      for (const logFile of logFiles) {
        fs.unlinkSync(logFile)
        cleanupActions.push(`删除 frontend/${logFile}`)
      }
    }

    // 清理后端
    const backendPath = path.join(projectRoot, 'backend')
    if (fs.existsSync(backendPath)) {
      process.chdir(backendPath)
      
      // 清理Python缓存
      const pycacheDirs = []
      const findPycache = (dir) => {
        const items = fs.readdirSync(dir, { withFileTypes: true })
        for (const item of items) {
          if (item.isDirectory()) {
            const fullPath = path.join(dir, item.name)
            if (item.name === '__pycache__') {
              pycacheDirs.push(fullPath)
            } else if (!item.name.startsWith('.')) {
              findPycache(fullPath)
            }
          }
        }
      }

      findPycache('.')
      for (const dir of pycacheDirs) {
        fs.rmSync(dir, { recursive: true, force: true })
        cleanupActions.push(`删除 ${dir}`)
      }

      // 清理日志文件
      const logFiles = fs.readdirSync('.').filter(file => file.endsWith('.log'))
      for (const logFile of logFiles) {
        fs.unlinkSync(logFile)
        cleanupActions.push(`删除 backend/${logFile}`)
      }
    }

    // 清理根目录
    process.chdir(projectRoot)
    const rootLogFiles = fs.readdirSync('.').filter(file => file.endsWith('.log'))
    for (const logFile of rootLogFiles) {
      fs.unlinkSync(logFile)
      cleanupActions.push(`删除 ${logFile}`)
    }

    return {
      actions: cleanupActions,
      message: `清理了 ${cleanupActions.length} 个文件/目录`
    }
  }

  // 检查依赖
  async checkDependencies() {
    const issues = []

    // 检查前端依赖
    const frontendPath = path.join(projectRoot, 'frontend')
    if (fs.existsSync(frontendPath)) {
      process.chdir(frontendPath)
      
      try {
        // 检查过期依赖
        const outdated = execSync('npm outdated --json', { encoding: 'utf8', stdio: 'pipe' })
        const outdatedPackages = JSON.parse(outdated || '{}')
        
        for (const [pkg, info] of Object.entries(outdatedPackages)) {
          issues.push({
            type: 'outdated',
            package: pkg,
            current: info.current,
            wanted: info.wanted,
            latest: info.latest,
            location: 'frontend'
          })
        }
      } catch (error) {
        // npm outdated 在没有过期包时会返回非零退出码
      }

      try {
        // 安全审计
        const audit = execSync('npm audit --json', { encoding: 'utf8', stdio: 'pipe' })
        const auditResult = JSON.parse(audit)
        
        if (auditResult.vulnerabilities) {
          for (const [pkg, vuln] of Object.entries(auditResult.vulnerabilities)) {
            issues.push({
              type: 'vulnerability',
              package: pkg,
              severity: vuln.severity,
              title: vuln.title,
              location: 'frontend'
            })
          }
        }
      } catch (error) {
        // 忽略审计错误
      }
    }

    // 检查后端依赖
    const backendPath = path.join(projectRoot, 'backend')
    if (fs.existsSync(backendPath)) {
      process.chdir(backendPath)
      
      try {
        // 检查Python包安全性
        const safety = execSync('pip list --format=json', { encoding: 'utf8' })
        const packages = JSON.parse(safety)
        
        // 这里可以添加更多的Python包检查逻辑
        // 例如使用safety库检查已知漏洞
      } catch (error) {
        // 忽略检查错误
      }
    }

    process.chdir(projectRoot)

    return {
      issues,
      summary: {
        outdated: issues.filter(i => i.type === 'outdated').length,
        vulnerabilities: issues.filter(i => i.type === 'vulnerability').length
      }
    }
  }

  // 更新依赖
  async updateDependencies() {
    const updates = []

    // 更新前端依赖
    const frontendPath = path.join(projectRoot, 'frontend')
    if (fs.existsSync(frontendPath)) {
      process.chdir(frontendPath)
      
      try {
        execSync('npm update', { stdio: 'pipe' })
        updates.push('更新前端依赖')
      } catch (error) {
        throw new Error(`前端依赖更新失败: ${error.message}`)
      }
    }

    // 更新后端依赖
    const backendPath = path.join(projectRoot, 'backend')
    if (fs.existsSync(backendPath)) {
      process.chdir(backendPath)
      
      try {
        execSync('pip install --upgrade -r requirements.txt', { stdio: 'pipe' })
        updates.push('更新后端依赖')
      } catch (error) {
        throw new Error(`后端依赖更新失败: ${error.message}`)
      }
    }

    process.chdir(projectRoot)

    return {
      updates,
      message: `完成 ${updates.length} 项依赖更新`
    }
  }

  // 格式化代码
  async formatCode() {
    const formatted = []

    // 格式化前端代码
    const frontendPath = path.join(projectRoot, 'frontend')
    if (fs.existsSync(frontendPath)) {
      process.chdir(frontendPath)
      
      try {
        execSync('npm run format', { stdio: 'pipe' })
        formatted.push('前端代码格式化')
      } catch (error) {
        // 忽略格式化错误
      }
    }

    // 格式化后端代码
    const backendPath = path.join(projectRoot, 'backend')
    if (fs.existsSync(backendPath)) {
      process.chdir(backendPath)
      
      try {
        execSync('python -m black app/', { stdio: 'pipe' })
        formatted.push('后端代码格式化')
      } catch (error) {
        // 忽略格式化错误
      }
    }

    process.chdir(projectRoot)

    return {
      formatted,
      message: `完成 ${formatted.length} 项代码格式化`
    }
  }

  // 修复代码检查问题
  async fixLinting() {
    const fixes = []

    // 修复前端代码问题
    const frontendPath = path.join(projectRoot, 'frontend')
    if (fs.existsSync(frontendPath)) {
      process.chdir(frontendPath)
      
      try {
        execSync('npm run lint:fix', { stdio: 'pipe' })
        fixes.push('前端代码问题修复')
      } catch (error) {
        // 忽略修复错误
      }
    }

    process.chdir(projectRoot)

    return {
      fixes,
      message: `完成 ${fixes.length} 项代码问题修复`
    }
  }

  // 优化项目
  async optimizeProject() {
    const optimizations = []

    // 分析包大小
    const frontendPath = path.join(projectRoot, 'frontend')
    if (fs.existsSync(frontendPath)) {
      process.chdir(frontendPath)
      
      try {
        // 运行构建分析
        execSync('npm run build:analyze', { stdio: 'pipe' })
        optimizations.push('构建分析完成')
      } catch (error) {
        // 忽略分析错误
      }
    }

    process.chdir(projectRoot)

    return {
      optimizations,
      message: `完成 ${optimizations.length} 项优化`
    }
  }

  // 安全审计
  async securityAudit() {
    const findings = []

    // 检查环境变量文件
    const envFiles = ['.env', 'frontend/.env', 'backend/.env']
    for (const envFile of envFiles) {
      const envPath = path.join(projectRoot, envFile)
      if (fs.existsSync(envPath)) {
        findings.push({
          type: 'env_file',
          file: envFile,
          message: '发现环境变量文件，确保不要提交到版本控制'
        })
      }
    }

    // 检查硬编码密钥
    try {
      const result = execSync('grep -r -i "password\\|secret\\|key" --include="*.ts" --include="*.js" --include="*.py" frontend/src backend/app || true', { encoding: 'utf8' })
      if (result.trim()) {
        const lines = result.split('\n').filter(line => line.trim())
        if (lines.length > 0) {
          findings.push({
            type: 'hardcoded_secrets',
            count: lines.length,
            message: `发现 ${lines.length} 处可能的硬编码敏感信息`
          })
        }
      }
    } catch (error) {
      // 忽略grep错误
    }

    return {
      findings,
      summary: `发现 ${findings.length} 个安全相关项目`
    }
  }

  // 更新文档
  async updateDocumentation() {
    const updates = []

    // 更新README中的最后更新时间
    const readmePath = path.join(projectRoot, 'README.md')
    if (fs.existsSync(readmePath)) {
      let content = fs.readFileSync(readmePath, 'utf8')
      const today = new Date().toISOString().split('T')[0]
      
      // 更新最后更新日期
      content = content.replace(
        /最新更新.*\(\d{4}-\d{2}-\d{2}\)/,
        `最新更新 (${today})`
      )
      
      fs.writeFileSync(readmePath, content)
      updates.push('更新README最后更新时间')
    }

    return {
      updates,
      message: `完成 ${updates.length} 项文档更新`
    }
  }

  // 生成维护报告
  generateReport() {
    console.log('\n' + '='.repeat(60))
    console.log(chalk.bold('📊 项目维护报告'))
    console.log('='.repeat(60))

    const successful = this.results.filter(r => r.status === 'success')
    const failed = this.results.filter(r => r.status === 'error')

    console.log(`✅ 成功任务: ${successful.length}`)
    console.log(`❌ 失败任务: ${failed.length}`)
    console.log(`⏱️  总耗时: ${this.results.reduce((sum, r) => sum + r.duration, 0)}ms`)

    if (failed.length > 0) {
      console.log('\n❌ 失败的任务:')
      for (const task of failed) {
        console.log(`  - ${task.name}: ${task.error}`)
      }
    }

    // 保存报告
    const report = {
      timestamp: new Date().toISOString(),
      results: this.results,
      summary: {
        total: this.results.length,
        successful: successful.length,
        failed: failed.length,
        totalDuration: this.results.reduce((sum, r) => sum + r.duration, 0)
      }
    }

    const reportPath = path.join(projectRoot, 'maintenance-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
  }

  // 列出所有任务
  listTasks() {
    console.log(chalk.blue('📋 可用的维护任务:'))
    for (const [key, task] of this.tasks) {
      console.log(`  ${chalk.cyan(key.padEnd(15))} - ${task.description}`)
    }
  }
}

// 命令行接口
const args = process.argv.slice(2)
const command = args[0]
const taskName = args[1]

const maintenance = new ProjectMaintenance()

if (command === 'run' && taskName) {
  maintenance.runTask(taskName).catch(error => {
    console.error(chalk.red('任务执行失败:'), error.message)
    process.exit(1)
  })
} else if (command === 'all') {
  maintenance.runAllTasks().catch(error => {
    console.error(chalk.red('维护执行失败:'), error.message)
    process.exit(1)
  })
} else if (command === 'list') {
  maintenance.listTasks()
} else {
  console.log(chalk.blue('🔧 项目维护工具'))
  console.log('')
  console.log('用法:')
  console.log('  node project-maintenance.js all              # 运行所有维护任务')
  console.log('  node project-maintenance.js run <task>       # 运行指定任务')
  console.log('  node project-maintenance.js list             # 列出所有任务')
  console.log('')
  console.log('示例:')
  console.log('  node project-maintenance.js run cleanup      # 清理项目')
  console.log('  node project-maintenance.js run deps-check   # 检查依赖')
}
