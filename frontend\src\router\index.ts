import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 主布局路由
    {
      path: '/',
      component: () => import('@/layouts/ProfessionalLayout.vue'),
      redirect: '/dashboard',
      children: [
        // Dashboard路由
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('@/views/Dashboard/SimpleDashboard.vue'),
          meta: {
            title: '投资仪表盘',
            icon: 'DataAnalysis',
            requiresAuth: false
          }
        },
      ],
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => {
        return import('@/views/Error/404.vue').catch(() => {
          // 如果404页面不存在，返回简单的404组件
          return {
            template: '<div style="text-align: center; padding: 2rem;"><h1>404 - 页面未找到</h1><p>抱歉，您访问的页面不存在</p></div>'
          }
        })
      },
      meta: {
        title: '页面未找到',
        requiresAuth: false,
      },
    },
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router

