import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 主布局路由
    {
      path: '/',
      component: () => import('@/layouts/ProfessionalLayout.vue'),
      redirect: '/dashboard',
      children: [
        // Dashboard路由
        {
          path: 'dashboard',
          name: 'dashboard',
          component: () => import('@/views/Dashboard/SimpleDashboard.vue'),
          meta: {
            title: '投资仪表盘',
            icon: 'DataAnalysis',
            requiresAuth: false
          }
        },
        // 市场路由
        {
          path: 'market',
          name: 'market',
          component: () => import('@/views/Market/MarketView.vue'),
          meta: {
            title: '市场行情',
            icon: 'TrendCharts',
            requiresAuth: false
          }
        },
        // 交易路由
        {
          path: 'trading',
          name: 'trading',
          component: () => import('@/views/Trading/TradingView.vue'),
          meta: {
            title: '智能交易',
            icon: 'Money',
            requiresAuth: false
          }
        },
        // 策略路由
        {
          path: 'strategy',
          name: 'strategy',
          component: () => import('@/views/Strategy/StrategyView.vue'),
          meta: {
            title: '策略研发',
            icon: 'DataBoard',
            requiresAuth: false
          }
        },
        // 投资组合路由
        {
          path: 'portfolio',
          name: 'portfolio',
          component: () => import('@/views/Portfolio/PortfolioView.vue'),
          meta: {
            title: '投资组合',
            icon: 'PieChart',
            requiresAuth: false
          }
        },
        // 回测路由
        {
          path: 'backtest',
          name: 'backtest',
          component: () => import('@/views/Backtest/BacktestView.vue'),
          meta: {
            title: '策略回测',
            icon: 'Refresh',
            requiresAuth: false
          }
        },
        // 风险管理路由
        {
          path: 'risk',
          name: 'risk',
          component: () => import('@/views/Risk/RiskView.vue'),
          meta: {
            title: '风险管理',
            icon: 'Warning',
            requiresAuth: false
          }
        },
        // 演示页面
        {
          path: 'demo',
          name: 'demo',
          component: () => import('@/views/Demo/DemoView.vue'),
          meta: {
            title: '功能演示',
            icon: 'Grid',
            requiresAuth: false
          }
        },
      ],
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/Error/404.vue'),
      meta: {
        title: '页面未找到',
        requiresAuth: false,
      },
    },
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router

