<template>
  <div class="professional-layout">
    <!-- 简化版导航栏 -->
    <header class="top-navbar">
      <div class="navbar-content">
        <div class="navbar-left">
          <div class="logo-section">
            <span class="app-title">🚀 量化投资平台</span>
          </div>
        </div>

        <nav class="navbar-nav">
          <router-link to="/dashboard" class="nav-item">📊 仪表盘</router-link>
          <router-link to="/market" class="nav-item">📈 市场</router-link>
          <router-link to="/trading" class="nav-item">💰 交易</router-link>
          <router-link to="/strategy" class="nav-item">🧠 策略</router-link>
          <router-link to="/portfolio" class="nav-item">📋 投资组合</router-link>
          <router-link to="/demo" class="nav-item">🎯 演示</router-link>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="content-wrapper">
        <!-- 路由视图 -->
        <router-view v-slot="{ Component, route }">
          <transition name="fade-slide" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 缓存的视图
const cachedViews = ref<string[]>([
  'Dashboard',
  'Market',
  'Portfolio',
  'Strategy'
])

// 监听路由变化
const handleRouteChange = () => {
  // 可以在这里添加路由变化的处理逻辑
}
</script>

<style scoped>
.professional-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.top-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 2rem;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  max-width: 1400px;
  margin: 0 auto;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-nav {
  display: flex;
  gap: 2rem;
}

.nav-item {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-2px);
}

.nav-item.router-link-active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.main-content {
  padding: 2rem;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 600px;
}

/* 路由过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-content {
    flex-direction: column;
    height: auto;
    padding: 1rem 0;
  }

  .navbar-nav {
    margin-top: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .main-content {
    padding: 1rem;
  }

  .content-wrapper {
    padding: 1rem;
  }
}
</style>
  console.log('Route changed to:', route.path)
}

onMounted(() => {
  handleRouteChange()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped lang="scss">
.professional-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .main-content {
    padding-top: 80px; // 为顶部导航留出空间
    min-height: calc(100vh - 80px);

    .content-wrapper {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;

      @media (max-width: 768px) {
        padding: 10px;
      }
    }
  }
}

// 页面切换动画
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

// 移动端适配
@media (max-width: 768px) {
  .professional-layout {
    .main-content {
      padding-top: 60px;

      .content-wrapper {
        padding: 10px;
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .professional-layout {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
  }
}
</style>
