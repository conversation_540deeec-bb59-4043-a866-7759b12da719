<template>
  <div class="simple-home">
    <div class="header">
      <h1>🚀 量化投资平台</h1>
      <p>专业级量化交易系统 - Vue 3应用已成功启动</p>
    </div>
    
    <div class="content">
      <div class="card-grid">
        <div class="card" @click="goTo('/dashboard')">
          <div class="card-icon">📊</div>
          <h3>投资仪表盘</h3>
          <p>查看投资概览和关键指标</p>
        </div>
        
        <div class="card" @click="goTo('/test')">
          <div class="card-icon">🧪</div>
          <h3>功能测试</h3>
          <p>测试各项功能和API连接</p>
        </div>
        
        <div class="card" @click="testAPI()">
          <div class="card-icon">🔌</div>
          <h3>API测试</h3>
          <p>测试后端API连接状态</p>
        </div>
        
        <div class="card" @click="showInfo()">
          <div class="card-icon">ℹ️</div>
          <h3>系统信息</h3>
          <p>查看应用和系统信息</p>
        </div>
      </div>
      
      <div class="status-section">
        <h3>🔍 系统状态</h3>
        <div class="status-grid">
          <div class="status-item">
            <span class="label">Vue版本:</span>
            <span class="value">{{ vueVersion }}</span>
          </div>
          <div class="status-item">
            <span class="label">路由状态:</span>
            <span class="value success">✅ 正常</span>
          </div>
          <div class="status-item">
            <span class="label">当前路由:</span>
            <span class="value">{{ $route.path }}</span>
          </div>
          <div class="status-item">
            <span class="label">API状态:</span>
            <span class="value" :class="apiStatus.class">{{ apiStatus.text }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { version } from 'vue'

const router = useRouter()
const vueVersion = version
const apiStatus = ref({ text: '检测中...', class: 'checking' })

const goTo = (path: string) => {
  router.push(path)
}

const testAPI = async () => {
  try {
    const response = await fetch('http://localhost:8000/health')
    if (response.ok) {
      alert('✅ API连接成功！')
      apiStatus.value = { text: '✅ 连接正常', class: 'success' }
    } else {
      alert('⚠️ API响应异常')
      apiStatus.value = { text: '⚠️ 响应异常', class: 'warning' }
    }
  } catch (error) {
    alert('❌ API连接失败: ' + (error as Error).message)
    apiStatus.value = { text: '❌ 连接失败', class: 'error' }
  }
}

const showInfo = () => {
  const info = {
    Vue版本: version,
    用户代理: navigator.userAgent,
    当前时间: new Date().toLocaleString(),
    页面URL: window.location.href
  }
  
  alert('系统信息:\n' + Object.entries(info).map(([k, v]) => `${k}: ${v}`).join('\n'))
}

onMounted(() => {
  console.log('🎉 SimpleHome组件已挂载')
  testAPI()
})
</script>

<style scoped>
.simple-home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.card h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.card p {
  color: #666;
  line-height: 1.5;
}

.status-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.status-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.label {
  font-weight: 600;
  color: #495057;
}

.value {
  font-family: monospace;
}

.value.success {
  color: #28a745;
}

.value.warning {
  color: #ffc107;
}

.value.error {
  color: #dc3545;
}

.value.checking {
  color: #6c757d;
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 2rem;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
