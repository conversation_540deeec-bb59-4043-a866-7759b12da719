"""
用户相关数据库模型
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import List, Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, ForeignKey, Index, Integer, 
    String, Text, Table
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


# 用户角色枚举
class UserRoleEnum(str, Enum):
    ADMIN = "admin"
    TRADER = "trader"
    ANALYST = "analyst"
    VIEWER = "viewer"


class UserStatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    DELETED = "deleted"


# 用户角色关联表
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True),
    Column('role_id', UUID(as_uuid=True), ForeignKey('roles.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now()),
)

# 角色权限关联表  
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', UUID(as_uuid=True), ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', UUID(as_uuid=True), ForeignKey('permissions.id'), primary_key=True),
    Column('created_at', DateTime, default=func.now()),
)


class User(Base):
    """用户表"""
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 基础信息
    username = Column(String(50), unique=True, nullable=False, index=True, comment="用户名")
    email = Column(String(255), unique=True, nullable=False, index=True, comment="邮箱")
    phone = Column(String(20), nullable=True, index=True, comment="手机号")
    
    # 认证信息
    password_hash = Column(String(255), nullable=False, comment="密码哈希")
    salt = Column(String(32), nullable=False, comment="密码盐值")
    
    # 状态信息
    status = Column(SQLEnum(UserStatusEnum), default=UserStatusEnum.ACTIVE, comment="用户状态")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_verified = Column(Boolean, default=False, comment="是否验证")
    
    # 登录信息
    last_login_at = Column(DateTime, nullable=True, comment="最后登录时间")
    last_login_ip = Column(String(45), nullable=True, comment="最后登录IP")
    failed_login_attempts = Column(Integer, default=0, comment="失败登录次数")
    locked_until = Column(DateTime, nullable=True, comment="锁定截止时间")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    settings = relationship("UserSettings", back_populates="user", uselist=False, cascade="all, delete-orphan")
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    
    # 交易相关关系
    account = relationship("Account", back_populates="user", uselist=False)
    orders = relationship("Order", back_populates="user")
    positions = relationship("Position", back_populates="user")
    strategies = relationship("Strategy", back_populates="user")

    # 索引
    __table_args__ = (
        Index('idx_users_username', 'username'),
        Index('idx_users_email', 'email'),
        Index('idx_users_status', 'status'),
        Index('idx_users_created_at', 'created_at'),
    )


class UserProfile(Base):
    """用户资料表"""
    __tablename__ = "user_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, unique=True)
    
    # 个人信息
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    nickname = Column(String(50), nullable=True, comment="昵称")
    avatar_url = Column(String(500), nullable=True, comment="头像URL")
    bio = Column(Text, nullable=True, comment="个人简介")
    
    # 联系信息
    address = Column(String(500), nullable=True, comment="地址")
    city = Column(String(50), nullable=True, comment="城市")
    country = Column(String(50), nullable=True, comment="国家")
    timezone = Column(String(50), default='Asia/Shanghai', comment="时区")
    language = Column(String(10), default='zh-CN', comment="语言偏好")
    
    # 交易相关信息
    trading_experience = Column(String(20), nullable=True, comment="交易经验")
    risk_tolerance = Column(String(20), nullable=True, comment="风险承受能力")
    investment_goal = Column(String(500), nullable=True, comment="投资目标")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="profile")


class UserSettings(Base):
    """用户设置表"""
    __tablename__ = "user_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, unique=True)
    
    # 界面设置
    theme = Column(String(20), default='light', comment="主题")
    layout_config = Column(Text, nullable=True, comment="布局配置(JSON)")
    dashboard_config = Column(Text, nullable=True, comment="仪表板配置(JSON)")
    
    # 通知设置
    email_notifications = Column(Boolean, default=True, comment="邮件通知")
    push_notifications = Column(Boolean, default=True, comment="推送通知")
    sms_notifications = Column(Boolean, default=False, comment="短信通知")
    trade_notifications = Column(Boolean, default=True, comment="交易通知")
    alert_notifications = Column(Boolean, default=True, comment="报警通知")
    
    # 交易设置
    default_order_type = Column(String(20), default='limit', comment="默认订单类型")
    auto_refresh_interval = Column(Integer, default=5, comment="自动刷新间隔(秒)")
    price_decimal_places = Column(Integer, default=2, comment="价格小数位数")
    
    # 安全设置
    two_factor_enabled = Column(Boolean, default=False, comment="双因子认证")
    login_ip_whitelist = Column(Text, nullable=True, comment="登录IP白名单")
    session_timeout = Column(Integer, default=3600, comment="会话超时时间(秒)")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="settings")


class Role(Base):
    """角色表"""
    __tablename__ = "roles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 基础信息
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    display_name = Column(String(100), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="角色描述")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_system = Column(Boolean, default=False, comment="是否系统角色")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")

    # 索引
    __table_args__ = (
        Index('idx_roles_name', 'name'),
        Index('idx_roles_is_active', 'is_active'),
    )


class Permission(Base):
    """权限表"""
    __tablename__ = "permissions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 基础信息
    name = Column(String(100), unique=True, nullable=False, comment="权限名称")
    display_name = Column(String(100), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="权限描述")
    
    # 权限分类
    category = Column(String(50), nullable=False, comment="权限分类")
    resource = Column(String(100), nullable=False, comment="资源")
    action = Column(String(50), nullable=False, comment="操作")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")

    # 索引
    __table_args__ = (
        Index('idx_permissions_name', 'name'),
        Index('idx_permissions_category', 'category'),
        Index('idx_permissions_resource_action', 'resource', 'action'),
    )


class UserRole(Base):
    """用户角色关系表（如果需要额外信息）"""
    __tablename__ = "user_role_assignments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey('roles.id'), nullable=False)
    
    # 额外信息
    assigned_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True, comment="分配者")
    assigned_at = Column(DateTime, default=func.now(), nullable=False, comment="分配时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 关系
    user = relationship("User", foreign_keys=[user_id])
    role = relationship("Role")
    assigner = relationship("User", foreign_keys=[assigned_by])

    # 索引
    __table_args__ = (
        Index('idx_user_role_assignments_user_id', 'user_id'),
        Index('idx_user_role_assignments_role_id', 'role_id'),
        Index('idx_user_role_assignments_active', 'is_active'),
    )