#!/usr/bin/env python3
"""
快速页面内容测试
"""

import requests

def test_page_content():
    try:
        response = requests.get("http://localhost:5174", timeout=10)
        content = response.text
        
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(content)} 字符")
        print("\n前1000个字符:")
        print("="*50)
        print(content[:1000])
        print("="*50)
        
        # 检查关键内容
        checks = {
            '量化投资平台': '量化投资平台' in content,
            'Vue应用启动': 'Vue应用已成功启动' in content,
            '仪表盘': '仪表盘' in content,
            '市场': '市场' in content,
            '交易': '交易' in content,
            '策略': '策略' in content,
            'router-view': 'router-view' in content,
            'Vue': 'vue' in content.lower()
        }
        
        print("\n关键内容检查:")
        for check, result in checks.items():
            status = "✅" if result else "❌"
            print(f"  {status} {check}: {result}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    test_page_content()
