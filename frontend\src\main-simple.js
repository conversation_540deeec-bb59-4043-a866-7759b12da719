/**
 * 简化版应用入口文件 - JavaScript版本
 * 用于快速启动和调试
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'

console.log('🚀 简化版应用启动中...')

// 创建简单的App组件
const App = {
  template: `
    <div id="app">
      <div style="padding: 20px;">
        <h1>🚀 量化投资平台</h1>
        <p>前端服务已成功启动！</p>
        <div style="margin: 20px 0;">
          <el-button type="primary" @click="testApi">测试API连接</el-button>
          <el-button @click="goToDashboard">进入仪表盘</el-button>
        </div>
        <div v-if="apiStatus" :style="{ color: apiStatus.success ? 'green' : 'red' }">
          {{ apiStatus.message }}
        </div>
        <router-view />
      </div>
    </div>
  `,
  data() {
    return {
      apiStatus: null
    }
  },
  methods: {
    async testApi() {
      try {
        const response = await fetch('/api/health')
        if (response.ok) {
          this.apiStatus = { success: true, message: '✅ API连接成功' }
        } else {
          this.apiStatus = { success: false, message: '❌ API连接失败' }
        }
      } catch (error) {
        this.apiStatus = { success: false, message: '❌ API连接错误: ' + error.message }
      }
    },
    goToDashboard() {
      this.$router.push('/dashboard')
    }
  }
}

// 创建简单的路由
const routes = [
  {
    path: '/',
    component: {
      template: '<div><h2>欢迎使用量化投资平台</h2><p>系统正在运行中...</p></div>'
    }
  },
  {
    path: '/dashboard',
    component: {
      template: `
        <div>
          <h2>📊 投资仪表盘</h2>
          <p>这里将显示您的投资概览</p>
          <el-button @click="$router.push('/')">返回首页</el-button>
        </div>
      `
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建Vue应用实例
const app = createApp(App)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 配置路由
app.use(router)

// 配置Element Plus
app.use(ElementPlus)

// 挂载应用
app.mount('#app')

console.log('✅ 简化版应用启动成功')
