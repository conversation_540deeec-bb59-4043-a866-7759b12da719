#!/usr/bin/env python3
"""
检查页面内容
"""

import requests

def check_page_content():
    try:
        response = requests.get("http://localhost:5173", timeout=10)
        content = response.text
        
        print("📄 页面内容分析:")
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(content)} 字符")
        print("\n前500个字符:")
        print("-" * 50)
        print(content[:500])
        print("-" * 50)
        
        # 检查关键元素
        checks = {
            "包含Vue应用": "router-view" in content or "Vue" in content,
            "包含量化投资平台": "量化投资平台" in content,
            "包含导航": "nav" in content or "navbar" in content,
            "包含仪表盘": "仪表盘" in content or "dashboard" in content,
            "包含交易": "交易" in content or "trading" in content,
            "包含市场": "市场" in content or "market" in content,
            "包含策略": "策略" in content or "strategy" in content,
        }
        
        print("\n🔍 关键元素检查:")
        for check, result in checks.items():
            status = "✅" if result else "❌"
            print(f"{status} {check}")
            
        # 检查是否是空白页面
        if len(content.strip()) < 100:
            print("\n⚠️ 警告: 页面内容过少，可能是空白页面")
        elif "<!DOCTYPE html>" not in content:
            print("\n⚠️ 警告: 不是标准HTML页面")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_page_content()
