/**
 * 简化版应用入口文件 - 修复版本
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

console.log('🚀 应用启动中...')

// 创建Vue应用实例
const app = createApp(App)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 导入Element Plus (按需加载)
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'

// 配置Element Plus
app.use(ElementPlus)

// 异步导入路由系统
console.log('📍 开始导入路由系统...')

// 使用动态导入确保兼容性
import('./router/index.ts').then(({ default: router }) => {
  console.log('✅ 路由系统导入成功')
  app.use(router)

  // 挂载应用
  app.mount('#app')

  console.log('✅ 专业Vue应用启动成功')
  console.log('🎯 使用完整路由配置')

}).catch(error => {
  console.error('❌ 路由导入失败:', error)

  // 异步导入作为后备
  import('./router').then(({ default: router }) => {
    console.log('✅ 异步路由加载成功')
    app.use(router)
    app.mount('#app')
    console.log('✅ Vue应用启动成功 (异步路由)')
  }).catch(asyncError => {
    console.error('❌ 异步路由也失败:', asyncError)

    // 最后的后备方案 - 创建基本应用
    const basicApp = createApp({
      template: `
        <div style="padding: 2rem; text-align: center; font-family: Arial, sans-serif;">
          <h1 style="color: #2c3e50;">🚀 量化投资平台</h1>
          <p style="color: #6b7280; margin: 1rem 0;">应用正在加载中...</p>
          <div style="margin: 2rem 0;">
            <button @click="reload" style="padding: 0.75rem 1.5rem; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">
              🔄 重新加载
            </button>
          </div>
          <p style="color: #909399; font-size: 0.9rem;">如果问题持续，请检查控制台错误信息</p>
        </div>
      `,
      methods: {
        reload() {
          window.location.reload()
        }
      }
    })
    basicApp.mount('#app')
    console.log('⚠️ 使用基本后备应用')
  })
}
