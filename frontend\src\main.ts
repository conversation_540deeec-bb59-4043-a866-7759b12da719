/**
 * 简化版应用入口文件 - 修复版本
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

console.log('🚀 应用启动中...')

// 创建Vue应用实例
const app = createApp(App)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 导入Element Plus (按需加载)
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'

// 配置Element Plus
app.use(ElementPlus)

// 导入完整路由系统
import('./router').then(({ default: router }) => {
  app.use(router)

  // 挂载应用
  app.mount('#app')

  console.log('✅ 专业Vue应用启动成功')
  console.log('🎯 使用完整路由配置')
}).catch(error => {
  console.error('❌ 完整路由加载失败，尝试简化版本:', error)

  // 如果完整路由失败，尝试简化路由
  import('./router/simple').then(({ default: simpleRouter }) => {
    app.use(simpleRouter)
    app.mount('#app')
    console.log('⚠️ 使用简化路由作为后备')
  }).catch(fallbackError => {
    console.error('❌ 所有路由都失败:', fallbackError)

    // 最后的后备方案
    const fallbackApp = createApp({
      template: `
        <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
          <h1 style="color: #2c3e50;">🚀 量化投资平台</h1>
          <p style="color: #dc3545;">应用启动失败，请检查配置</p>
          <button @click="reload" style="margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            🔄 重新加载
          </button>
        </div>
      `,
      methods: {
        reload() { window.location.reload() }
      }
    })
    fallbackApp.mount('#app')
  })
})
