/**
 * 简化版应用入口文件 - 修复版本
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

console.log('🚀 应用启动中...')

// 创建Vue应用实例
const app = createApp(App)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 导入Element Plus (按需加载)
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'

// 配置Element Plus
app.use(ElementPlus)

// 导入简化路由
import('./router/simple').then(({ default: router }) => {
  app.use(router)

  // 挂载应用
  app.mount('#app')

  console.log('✅ Vue应用启动成功')
  console.log('🎯 使用简化路由配置')
}).catch(error => {
  console.error('❌ 路由加载失败:', error)

  // 创建一个简单的应用作为后备
  const simpleApp = createApp({
    template: `
      <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif;">
        <h1 style="color: #2c3e50;">🚀 量化投资平台</h1>
        <p style="color: #6c757d;">Vue应用启动中...</p>
        <p style="color: #dc3545; margin-top: 20px;">路由加载失败: {{ error }}</p>
        <button @click="reload" style="margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
          🔄 重新加载
        </button>
      </div>
    `,
    data() {
      return { error: error.message }
    },
    methods: {
      reload() {
        window.location.reload()
      }
    }
  })

  simpleApp.mount('#app')
})
