/**
 * 量化投资平台主入口文件 - 修复版
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import QuantPlatform from './QuantPlatform.vue'

console.log('🚀 量化投资平台启动中...')

// 创建Vue应用实例
const app = createApp(QuantPlatform)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('应用错误:', err)
  console.error('错误信息:', info)
}

// 挂载应用
app.mount('#app')
console.log('✅ 量化投资平台启动成功')
