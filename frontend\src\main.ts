/**
 * 简化版应用入口文件 - 修复版本
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

console.log('🚀 应用启动中...')

// 创建Vue应用实例
const app = createApp(App)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 导入Element Plus (按需加载)
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'

// 配置Element Plus
app.use(ElementPlus)

// 导入完整路由系统
import('./router').then(({ default: router }) => {
  app.use(router)

  // 挂载应用
  app.mount('#app')

  console.log('✅ 专业Vue应用启动成功')
  console.log('🎯 使用完整路由配置')
}).catch(error => {
  console.error('❌ 路由加载失败:', error)

  // 创建最小化后备应用
  const fallbackApp = createApp({
    template: `
      <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
        <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px;">
          <h1 style="color: #2c3e50; margin-bottom: 1rem;">🚀 量化投资平台</h1>
          <p style="color: #6b7280; margin-bottom: 2rem;">正在初始化应用...</p>
          <div style="margin-bottom: 2rem;">
            <div style="width: 40px; height: 40px; border: 4px solid #e4e7ed; border-top: 4px solid #409eff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
          </div>
          <button @click="reload" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1rem; transition: transform 0.2s;">
            🔄 重新加载
          </button>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        button:hover {
          transform: translateY(-2px);
        }
      </style>
    `,
    methods: {
      reload() {
        console.log('🔄 重新加载应用...')
        window.location.reload()
      }
    }
  })
  fallbackApp.mount('#app')
})
