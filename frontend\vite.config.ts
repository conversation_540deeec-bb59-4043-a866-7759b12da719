/**
 * Vite 配置文件
 *
 * 量化交易平台前端构建配置
 * 支持开发、测试、生产环境的差异化配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */

import { defineConfig, loadEnv, type UserConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { createHtmlPlugin } from 'vite-plugin-html'
import { compression } from 'vite-plugin-compression2'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// ===== 路径解析工具 =====
const pathResolve = (dir: string): string => resolve(process.cwd(), '.', dir)

// ===== 环境变量类型定义 =====
interface AppEnv {
  VITE_APP_TITLE: string
  VITE_APP_DESCRIPTION: string
  VITE_APP_KEYWORDS: string
  VITE_API_BASE_URL: string
  VITE_WS_BASE_URL: string
  VITE_ENABLE_MOCK: string
  VITE_ENABLE_PWA: string
  VITE_BUILD_ANALYZE: string
}

export default defineConfig(({ mode, command }): UserConfig => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '') as unknown as AppEnv

  // 环境判断
  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'
  const isBuild = command === 'build'

  console.log(`🚀 构建模式: ${mode}`)
  console.log(`📦 构建命令: ${command}`)
  console.log(`🌍 API地址: ${env.VITE_API_BASE_URL}`)

  return {
    plugins: [
      vue({
        script: {
          defineModel: true,
          propsDestructure: true
        }
      }),

      // 自动导入
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            'element-plus': [
              'ElMessage',
              'ElMessageBox',
              'ElNotification',
              'ElLoading'
            ],
            '@/utils/format/financial': [
              'formatCurrency',
              'formatPrice',
              'formatPercent',
              'formatVolume'
            ]
          }
        ],
        resolvers: [ElementPlusResolver()],
        dts: 'src/types/auto-imports.d.ts',
        eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json'
        }
      }),

      // 组件自动导入
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: false  // 禁用自动样式导入
          })
        ],
        dts: 'src/types/components.d.ts',
        dirs: ['src/components'],
        extensions: ['vue', 'tsx'],
        include: [/\.vue$/, /\.vue\?vue/, /\.tsx$/]
      }),

      // HTML 模板处理
      createHtmlPlugin({
        minify: isProduction,
        inject: {
          data: {
            title: env.VITE_APP_TITLE || 'Quant Platform',
            description: env.VITE_APP_DESCRIPTION || 'Professional Quantitative Trading Platform',
            keywords: env.VITE_APP_KEYWORDS || 'quantitative trading, algorithmic trading, financial platform'
          }
        }
      }),

      // 打包分析
      isProduction && visualizer({
        filename: 'dist/bundle-analysis.html',
        open: false,
        gzipSize: true,
        brotliSize: true,
        template: 'treemap' // 使用树状图显示
      }),

      // 生产环境压缩
      isProduction && compression({
        algorithm: 'gzip',
        threshold: 1024,
        deleteOriginalAssets: false
      }),

      // Brotli 压缩
      isProduction && compression({
        algorithm: 'brotliCompress',
        threshold: 1024,
        deleteOriginalAssets: false
      })
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@views': resolve(__dirname, 'src/views'),
        '@stores': resolve(__dirname, 'src/stores'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@api': resolve(__dirname, 'src/api'),
        '@types': resolve(__dirname, 'src/types'),
        '@assets': resolve(__dirname, 'src/assets'),
        '@composables': resolve(__dirname, 'src/composables'),
        '@layouts': resolve(__dirname, 'src/layouts'),
        '@router': resolve(__dirname, 'src/router'),
        '@plugins': resolve(__dirname, 'src/plugins'),
        '@config': resolve(__dirname, 'src/config'),
        '@directives': resolve(__dirname, 'src/directives')
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          // Remove additionalData to prevent global variable conflicts
        }
      }
      // CSS 代码分割在 build.cssCodeSplit 中设置
    },

    server: {
      port: 5173,
      host: '0.0.0.0',
      open: false,
      cors: true,
      strictPort: false,
      proxy: {
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err)
            })
            proxy.on('proxyReq', (_proxyReq, req, _res) => {
              console.log('Sending Request to the Target:', req.method, req.url)
            })
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url)
            })
          }
        },
        '/api/v1/ws': {
          target: 'ws://localhost:8000',
          ws: true,
          changeOrigin: true
        }
      }
    },

    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: !isProduction,
      minify: isProduction ? 'terser' : false,

      // 优化资源预加载
      modulePreload: {
        polyfill: false,
        resolveDependencies: (filename, deps) => {
          // 只预加载核心依赖
          return deps.filter(dep =>
            dep.includes('vue') ||
            dep.includes('echarts') ||
            dep.includes('element-plus')
          )
        }
      },

      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.warn'],
          // 移除无用代码
          dead_code: true,
          unused: true,
          // 压缩选项
          passes: 2,
          keep_infinity: true,
          reduce_vars: true
        },
        mangle: {
          safari10: true
        },
        format: {
          comments: false
        }
      } : {},

      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name?.split('.') || []
            const extType = info[info.length - 1]

            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name || '')) {
              return 'assets/media/[name]-[hash].[ext]'
            }

            if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name || '')) {
              return 'assets/images/[name]-[hash].[ext]'
            }

            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name || '')) {
              return 'assets/fonts/[name]-[hash].[ext]'
            }

            return `assets/${extType}/[name]-[hash].[ext]`
          },

          manualChunks: (id) => {
            // Vue 核心框架 - 最高优先级
            if (id.includes('node_modules/vue/') && !id.includes('vue-router')) {
              return 'vue-core'
            }

            // Vue 路由 - 独立分块
            if (id.includes('node_modules/vue-router')) {
              return 'vue-router'
            }

            // Pinia 状态管理 - 独立分块
            if (id.includes('node_modules/pinia')) {
              return 'pinia'
            }

            // Element Plus 核心
            if (id.includes('node_modules/element-plus/es/components')) {
              // 按组件类型分组
              if (id.includes('/form/') || id.includes('/input/') || id.includes('/select/')) {
                return 'element-form'
              }
              if (id.includes('/table/') || id.includes('/pagination/')) {
                return 'element-table'
              }
              if (id.includes('/dialog/') || id.includes('/drawer/') || id.includes('/popover/')) {
                return 'element-overlay'
              }
              return 'element-base'
            }

            // Element Plus 主题和样式
            if (id.includes('node_modules/element-plus') && id.includes('/theme-chalk/')) {
              return 'element-styles'
            }

            // ECharts 图表库 - 按需分块
            if (id.includes('node_modules/echarts')) {
              if (id.includes('/core/')) {
                return 'echarts-core'
              }
              if (id.includes('/charts/')) {
                return 'echarts-charts'
              }
              if (id.includes('/components/')) {
                return 'echarts-components'
              }
              return 'echarts-misc'
            }

            // 工具库 - 按功能分组
            if (id.includes('node_modules/axios')) {
              return 'http-client'
            }

            if (id.includes('node_modules/lodash-es')) {
              return 'lodash-utils'
            }

            if (id.includes('node_modules/dayjs')) {
              return 'date-utils'
            }

            if (id.includes('node_modules/decimal.js') ||
                id.includes('node_modules/big.js') ||
                id.includes('node_modules/numeral')) {
              return 'math-utils'
            }

            // WebSocket 相关
            if (id.includes('node_modules/socket.io-client') ||
                id.includes('node_modules/ws')) {
              return 'websocket'
            }

            // 页面路由按需加载 - 确保首屏不包含
            if (id.includes('src/views/')) {
              if (id.includes('/Dashboard/')) {
                return 'page-dashboard'
              }
              if (id.includes('/Market/')) {
                return 'page-market'
              }
              if (id.includes('/Trading/')) {
                return 'page-trading'
              }
              if (id.includes('/Strategy/')) {
                return 'page-strategy'
              }
              if (id.includes('/Backtest/')) {
                return 'page-backtest'
              }
              if (id.includes('/Settings/')) {
                return 'page-settings'
              }
              return 'page-misc'
            }

            // 业务组件按模块分组
            if (id.includes('src/components/')) {
              if (id.includes('/Chart/')) {
                return 'components-chart'
              }
              if (id.includes('/Trading/')) {
                return 'components-trading'
              }
              if (id.includes('/Market/')) {
                return 'components-market'
              }
              return 'components-common'
            }

            // Composables 按功能分组
            if (id.includes('src/composables/')) {
              if (id.includes('/trading/')) {
                return 'composables-trading'
              }
              if (id.includes('/chart/')) {
                return 'composables-chart'
              }
              if (id.includes('/strategy/')) {
                return 'composables-strategy'
              }
              return 'composables-common'
            }

            // 其他第三方库
            if (id.includes('node_modules')) {
              return 'vendor-misc'
            }

            // 默认情况
            return undefined
          }
        },

        // 外部依赖 - 不打包进bundle
        external: isProduction ? [] : [],

        // 输入配置
        input: {
          main: resolve(__dirname, 'index.html')
        }
      },

      // 构建性能优化
      chunkSizeWarningLimit: 250, // 设置为250KB警告阈值
      reportCompressedSize: false, // 禁用压缩大小报告以提高构建速度

      // 静态资源处理
      assetsInlineLimit: 4096, // 小于4KB的资源内联

      // CSS 代码分割
      cssCodeSplit: true,

      // 启用 CSS 压缩
      cssMinify: isProduction
    },

    optimizeDeps: {
      force: isDevelopment,
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus/es',
        'element-plus/es/components/button/style/css',
        'element-plus/es/components/input/style/css',
        'element-plus/es/components/form/style/css',
        'element-plus/es/components/table/style/css',
        'element-plus/es/components/dialog/style/css',
        'element-plus/es/components/message/style/css',
        'element-plus/es/components/loading/style/css',
        'echarts/core',
        'echarts/charts',
        'echarts/components',
        'echarts/renderers',
        'axios',
        'lodash-es',
        'dayjs',
        'decimal.js'
      ],
      exclude: [
        '@vueuse/core'
      ],
      // 添加预构建配置
      entries: [
        'src/main.ts',
        'src/router/index.ts',
        'src/stores/index.ts'
      ]
    },

    define: {
      __VUE_OPTIONS_API__: false,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    },

    // 开发时性能优化
    esbuild: {
      drop: isProduction ? ['console', 'debugger'] : [],
      legalComments: 'none',
      target: 'es2015'
    },

    // 预构建优化配置已在上面定义
  }
})
