#!/usr/bin/env node

/**
 * 项目健康检查工具
 * 检查前后端服务状态、依赖完整性、代码质量等
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import chalk from 'chalk'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.resolve(__dirname, '..')

class HealthChecker {
  constructor() {
    this.results = {
      overall: 'unknown',
      checks: {},
      timestamp: new Date().toISOString(),
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      }
    }
  }

  // 运行所有健康检查
  async runAllChecks() {
    console.log(chalk.blue('🔍 开始项目健康检查...\n'))

    const checks = [
      { name: 'project-structure', fn: this.checkProjectStructure },
      { name: 'dependencies', fn: this.checkDependencies },
      { name: 'frontend-build', fn: this.checkFrontendBuild },
      { name: 'backend-imports', fn: this.checkBackendImports },
      { name: 'code-quality', fn: this.checkCodeQuality },
      { name: 'security', fn: this.checkSecurity },
      { name: 'documentation', fn: this.checkDocumentation }
    ]

    for (const check of checks) {
      try {
        console.log(chalk.yellow(`⏳ 检查 ${check.name}...`))
        const result = await check.fn.call(this)
        this.results.checks[check.name] = result
        
        if (result.status === 'pass') {
          this.results.summary.passed++
          console.log(chalk.green(`✅ ${check.name}: ${result.message}`))
        } else if (result.status === 'warning') {
          this.results.summary.warnings++
          console.log(chalk.yellow(`⚠️  ${check.name}: ${result.message}`))
        } else {
          this.results.summary.failed++
          console.log(chalk.red(`❌ ${check.name}: ${result.message}`))
        }
      } catch (error) {
        this.results.checks[check.name] = {
          status: 'fail',
          message: `检查失败: ${error.message}`,
          error: error.toString()
        }
        this.results.summary.failed++
        console.log(chalk.red(`❌ ${check.name}: 检查失败 - ${error.message}`))
      }
      console.log('')
    }

    this.calculateOverallStatus()
    this.generateReport()
  }

  // 检查项目结构
  checkProjectStructure() {
    const requiredPaths = [
      'frontend/src',
      'frontend/package.json',
      'backend/app',
      'backend/requirements.txt',
      'docs',
      'scripts',
      'README.md'
    ]

    const missingPaths = []
    for (const requiredPath of requiredPaths) {
      const fullPath = path.join(projectRoot, requiredPath)
      if (!fs.existsSync(fullPath)) {
        missingPaths.push(requiredPath)
      }
    }

    if (missingPaths.length === 0) {
      return {
        status: 'pass',
        message: '项目结构完整'
      }
    } else {
      return {
        status: 'fail',
        message: `缺少必要的文件/目录: ${missingPaths.join(', ')}`
      }
    }
  }

  // 检查依赖
  checkDependencies() {
    const issues = []

    // 检查前端依赖
    try {
      const frontendPath = path.join(projectRoot, 'frontend')
      process.chdir(frontendPath)
      
      // 检查package.json是否存在
      const packageJsonPath = path.join(frontendPath, 'package.json')
      if (!fs.existsSync(packageJsonPath)) {
        issues.push('前端package.json不存在')
      } else {
        // 检查node_modules
        const nodeModulesPath = path.join(frontendPath, 'node_modules')
        if (!fs.existsSync(nodeModulesPath)) {
          issues.push('前端依赖未安装 (node_modules不存在)')
        }
      }
    } catch (error) {
      issues.push(`前端依赖检查失败: ${error.message}`)
    }

    // 检查后端依赖
    try {
      const backendPath = path.join(projectRoot, 'backend')
      const requirementsPath = path.join(backendPath, 'requirements.txt')
      
      if (!fs.existsSync(requirementsPath)) {
        issues.push('后端requirements.txt不存在')
      } else {
        // 尝试导入主模块
        process.chdir(backendPath)
        try {
          execSync('python -c "import app.main"', { stdio: 'pipe' })
        } catch (error) {
          issues.push('后端模块导入失败，可能缺少依赖')
        }
      }
    } catch (error) {
      issues.push(`后端依赖检查失败: ${error.message}`)
    }

    process.chdir(projectRoot)

    if (issues.length === 0) {
      return {
        status: 'pass',
        message: '依赖检查通过'
      }
    } else {
      return {
        status: 'fail',
        message: `依赖问题: ${issues.join(', ')}`
      }
    }
  }

  // 检查前端构建
  checkFrontendBuild() {
    try {
      const frontendPath = path.join(projectRoot, 'frontend')
      process.chdir(frontendPath)

      // 检查TypeScript类型
      try {
        execSync('npm run type-check', { stdio: 'pipe' })
      } catch (error) {
        return {
          status: 'fail',
          message: 'TypeScript类型检查失败'
        }
      }

      // 检查ESLint
      try {
        execSync('npm run lint:check', { stdio: 'pipe' })
      } catch (error) {
        return {
          status: 'warning',
          message: 'ESLint检查发现问题'
        }
      }

      process.chdir(projectRoot)
      return {
        status: 'pass',
        message: '前端代码质量检查通过'
      }
    } catch (error) {
      process.chdir(projectRoot)
      return {
        status: 'fail',
        message: `前端构建检查失败: ${error.message}`
      }
    }
  }

  // 检查后端导入
  checkBackendImports() {
    try {
      const backendPath = path.join(projectRoot, 'backend')
      process.chdir(backendPath)

      // 检查主模块导入
      execSync('python -c "import app.main; print(\'Backend imports successfully\')"', { stdio: 'pipe' })

      // 检查关键模块
      const modules = [
        'app.api.v1',
        'app.core.config',
        'app.services',
        'app.models'
      ]

      for (const module of modules) {
        try {
          execSync(`python -c "import ${module}"`, { stdio: 'pipe' })
        } catch (error) {
          process.chdir(projectRoot)
          return {
            status: 'fail',
            message: `模块 ${module} 导入失败`
          }
        }
      }

      process.chdir(projectRoot)
      return {
        status: 'pass',
        message: '后端模块导入检查通过'
      }
    } catch (error) {
      process.chdir(projectRoot)
      return {
        status: 'fail',
        message: `后端导入检查失败: ${error.message}`
      }
    }
  }

  // 检查代码质量
  checkCodeQuality() {
    const issues = []

    // 检查是否有TODO/FIXME注释过多
    try {
      const result = execSync('grep -r "TODO\\|FIXME\\|XXX" frontend/src backend/app --exclude-dir=node_modules || true', { encoding: 'utf8' })
      const todoCount = result.split('\n').filter(line => line.trim()).length
      
      if (todoCount > 20) {
        issues.push(`发现 ${todoCount} 个待办事项，建议及时处理`)
      }
    } catch (error) {
      // grep命令在Windows上可能不可用，忽略错误
    }

    // 检查大文件
    try {
      const largeFiles = []
      const checkLargeFiles = (dir, maxSize = 1000) => {
        const files = fs.readdirSync(dir, { withFileTypes: true })
        for (const file of files) {
          const fullPath = path.join(dir, file.name)
          if (file.isDirectory() && !file.name.includes('node_modules') && !file.name.includes('.git')) {
            checkLargeFiles(fullPath, maxSize)
          } else if (file.isFile() && (file.name.endsWith('.ts') || file.name.endsWith('.vue') || file.name.endsWith('.py'))) {
            const stats = fs.statSync(fullPath)
            if (stats.size > maxSize * 1024) { // 转换为字节
              largeFiles.push(`${fullPath} (${Math.round(stats.size / 1024)}KB)`)
            }
          }
        }
      }

      checkLargeFiles(path.join(projectRoot, 'frontend/src'))
      checkLargeFiles(path.join(projectRoot, 'backend/app'))

      if (largeFiles.length > 0) {
        issues.push(`发现大文件: ${largeFiles.slice(0, 3).join(', ')}${largeFiles.length > 3 ? '...' : ''}`)
      }
    } catch (error) {
      // 忽略文件检查错误
    }

    if (issues.length === 0) {
      return {
        status: 'pass',
        message: '代码质量检查通过'
      }
    } else {
      return {
        status: 'warning',
        message: issues.join('; ')
      }
    }
  }

  // 检查安全性
  checkSecurity() {
    const issues = []

    // 检查是否有硬编码的密钥或密码
    try {
      const sensitivePatterns = [
        'password.*=.*["\'][^"\']{8,}["\']',
        'secret.*=.*["\'][^"\']{16,}["\']',
        'api_key.*=.*["\'][^"\']{16,}["\']'
      ]

      for (const pattern of sensitivePatterns) {
        try {
          const result = execSync(`grep -r -i "${pattern}" frontend/src backend/app --exclude-dir=node_modules || true`, { encoding: 'utf8' })
          if (result.trim()) {
            issues.push('发现可能的硬编码敏感信息')
            break
          }
        } catch (error) {
          // 忽略grep错误
        }
      }
    } catch (error) {
      // 忽略安全检查错误
    }

    // 检查环境变量文件
    const envFiles = ['.env', 'frontend/.env', 'backend/.env']
    for (const envFile of envFiles) {
      const envPath = path.join(projectRoot, envFile)
      if (fs.existsSync(envPath)) {
        issues.push(`发现环境变量文件 ${envFile}，确保不要提交到版本控制`)
      }
    }

    if (issues.length === 0) {
      return {
        status: 'pass',
        message: '安全检查通过'
      }
    } else {
      return {
        status: 'warning',
        message: issues.join('; ')
      }
    }
  }

  // 检查文档
  checkDocumentation() {
    const requiredDocs = [
      'README.md',
      'docs/DEVELOPMENT_GUIDE.md',
      'docs/API_DOCUMENTATION.md',
      'PROJECT_STRUCTURE.md'
    ]

    const missingDocs = []
    for (const doc of requiredDocs) {
      const docPath = path.join(projectRoot, doc)
      if (!fs.existsSync(docPath)) {
        missingDocs.push(doc)
      }
    }

    if (missingDocs.length === 0) {
      return {
        status: 'pass',
        message: '文档完整'
      }
    } else {
      return {
        status: 'warning',
        message: `缺少文档: ${missingDocs.join(', ')}`
      }
    }
  }

  // 计算总体状态
  calculateOverallStatus() {
    const { passed, failed, warnings } = this.results.summary
    
    if (failed > 0) {
      this.results.overall = 'unhealthy'
    } else if (warnings > 0) {
      this.results.overall = 'warning'
    } else {
      this.results.overall = 'healthy'
    }
  }

  // 生成报告
  generateReport() {
    const { overall, summary } = this.results
    
    console.log('\n' + '='.repeat(60))
    console.log(chalk.bold('📊 健康检查报告'))
    console.log('='.repeat(60))
    
    // 总体状态
    const statusColor = overall === 'healthy' ? 'green' : overall === 'warning' ? 'yellow' : 'red'
    const statusIcon = overall === 'healthy' ? '✅' : overall === 'warning' ? '⚠️' : '❌'
    console.log(`${statusIcon} 总体状态: ${chalk[statusColor](overall.toUpperCase())}`)
    
    // 统计信息
    console.log(`\n📈 检查统计:`)
    console.log(`  ${chalk.green('✅ 通过:')} ${summary.passed}`)
    console.log(`  ${chalk.yellow('⚠️  警告:')} ${summary.warnings}`)
    console.log(`  ${chalk.red('❌ 失败:')} ${summary.failed}`)
    
    // 建议
    console.log(`\n💡 建议:`)
    if (summary.failed > 0) {
      console.log(`  - 优先修复 ${summary.failed} 个失败的检查项`)
    }
    if (summary.warnings > 0) {
      console.log(`  - 关注 ${summary.warnings} 个警告项，考虑改进`)
    }
    if (overall === 'healthy') {
      console.log(`  - 项目状态良好，继续保持！`)
    }
    
    // 保存报告
    const reportPath = path.join(projectRoot, 'health-check-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2))
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
  }
}

// 运行健康检查
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new HealthChecker()
  checker.runAllChecks().catch(error => {
    console.error(chalk.red('健康检查运行失败:'), error)
    process.exit(1)
  })
}

export { HealthChecker }
