#!/usr/bin/env python3
"""
简单的后端服务用于测试登录功能
"""

import sqlite3
import bcrypt
import jwt
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 创建FastAPI应用
app = FastAPI(title="简单后端服务", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: dict

# 数据库连接
def get_db_connection():
    """获取数据库连接"""
    return sqlite3.connect("backend/app.db")

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=30)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def authenticate_user(username: str, password: str):
    """验证用户"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        cursor.execute(
            "SELECT id, username, email, full_name, hashed_password, role FROM users WHERE username = ?",
            (username,)
        )
        user = cursor.fetchone()
        
        if not user:
            return None
        
        user_id, username, email, full_name, hashed_password, role = user
        
        if not verify_password(password, hashed_password):
            return None
        
        return {
            "id": user_id,
            "username": username,
            "email": email,
            "full_name": full_name,
            "role": role
        }
    finally:
        conn.close()

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/v1/auth/login-json", response_model=LoginResponse)
async def login_json(credentials: LoginRequest):
    """JSON格式登录"""
    print(f"收到登录请求: {credentials.username}")
    
    # 验证用户
    user = authenticate_user(credentials.username, credentials.password)
    if not user:
        print(f"用户验证失败: {credentials.username}")
        raise HTTPException(
            status_code=401,
            detail="用户名或密码错误"
        )
    
    print(f"用户验证成功: {user}")
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(user["id"]), "username": user["username"]},
        expires_delta=access_token_expires
    )
    
    # 构建响应
    response = {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": 1800,  # 30分钟
        "user": {
            "id": user["id"],
            "username": user["username"],
            "email": user["email"],
            "full_name": user["full_name"],
            "role": user["role"],
            "permissions": []
        }
    }
    
    print(f"登录成功，返回响应: {response}")
    return response

@app.post("/api/v1/auth/login")
async def login_form(username: str, password: str):
    """表单格式登录"""
    credentials = LoginRequest(username=username, password=password)
    return await login_json(credentials)

@app.get("/api/v1/users/me")
async def get_current_user():
    """获取当前用户信息"""
    # 简化版本，返回模拟数据
    return {
        "id": 1,
        "username": "demo",
        "email": "<EMAIL>",
        "full_name": "演示用户",
        "role": "user"
    }

@app.get("/api/v1/market/stocks")
async def get_stocks():
    """获取股票列表"""
    return {
        "success": True,
        "data": {
            "items": [
                {
                    "symbol": "000001.SZ",
                    "name": "平安银行",
                    "current_price": 12.50,
                    "change": 0.15,
                    "change_percent": 1.22
                },
                {
                    "symbol": "600036.SH", 
                    "name": "招商银行",
                    "current_price": 45.20,
                    "change": -0.30,
                    "change_percent": -0.66
                }
            ],
            "total": 2,
            "page": 1,
            "size": 20
        }
    }

if __name__ == "__main__":
    print("启动简单后端服务...")
    print("健康检查: http://localhost:8000/health")
    print("登录API: http://localhost:8000/api/v1/auth/login-json")
    print("用户信息: demo/demo123, admin/admin123, test/test123")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
