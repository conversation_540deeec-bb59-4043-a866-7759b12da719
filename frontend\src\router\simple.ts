/**
 * 简化路由配置 - 用于快速启动
 */
import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/SimpleHome.vue')
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/SimpleDashboard.vue')
    },
    {
      path: '/test',
      name: 'Test',
      component: () => import('@/views/SimpleTest.vue')
    }
  ]
})

export default router
