<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台 - 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .status-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 量化投资平台</h1>
            <p>前端服务测试页面</p>
        </div>
        
        <div class="status-card">
            <h2>📊 系统状态</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-icon">🌐</div>
                    <h3>前端服务</h3>
                    <p id="frontend-status" class="success">✅ 正常运行</p>
                </div>
                <div class="status-item">
                    <div class="status-icon">🔗</div>
                    <h3>后端API</h3>
                    <p id="backend-status">🔄 检测中...</p>
                </div>
                <div class="status-item">
                    <div class="status-icon">📡</div>
                    <h3>WebSocket</h3>
                    <p id="websocket-status">🔄 检测中...</p>
                </div>
                <div class="status-item">
                    <div class="status-icon">💾</div>
                    <h3>数据存储</h3>
                    <p id="storage-status">🔄 检测中...</p>
                </div>
            </div>
        </div>
        
        <div class="status-card">
            <h2>🧪 功能测试</h2>
            <div style="text-align: center;">
                <button class="btn" onclick="testAPI()">测试API连接</button>
                <button class="btn" onclick="testWebSocket()">测试WebSocket</button>
                <button class="btn" onclick="testStorage()">测试本地存储</button>
                <button class="btn" onclick="clearLog()">清空日志</button>
            </div>
            <div id="log" class="log"></div>
        </div>
        
        <div class="status-card">
            <h2>🔗 快速导航</h2>
            <div style="text-align: center;">
                <button class="btn" onclick="window.location.href='/'">主页</button>
                <button class="btn" onclick="window.location.href='/dashboard'">仪表盘</button>
                <button class="btn" onclick="window.location.href='/market'">市场数据</button>
                <button class="btn" onclick="window.location.href='/trading'">交易终端</button>
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 测试API连接
        async function testAPI() {
            log('🔄 开始测试API连接...');
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    log('✅ API连接成功: ' + JSON.stringify(data), 'success');
                    document.getElementById('backend-status').innerHTML = '<span class="success">✅ 连接正常</span>';
                } else {
                    log('❌ API连接失败: HTTP ' + response.status, 'error');
                    document.getElementById('backend-status').innerHTML = '<span class="error">❌ 连接失败</span>';
                }
            } catch (error) {
                log('❌ API连接错误: ' + error.message, 'error');
                document.getElementById('backend-status').innerHTML = '<span class="error">❌ 连接错误</span>';
            }
        }
        
        // 测试WebSocket连接
        function testWebSocket() {
            log('🔄 开始测试WebSocket连接...');
            try {
                const ws = new WebSocket('ws://localhost:8000/api/v1/ws');
                
                ws.onopen = function() {
                    log('✅ WebSocket连接成功', 'success');
                    document.getElementById('websocket-status').innerHTML = '<span class="success">✅ 连接正常</span>';
                    ws.close();
                };
                
                ws.onerror = function(error) {
                    log('❌ WebSocket连接错误: ' + error, 'error');
                    document.getElementById('websocket-status').innerHTML = '<span class="error">❌ 连接错误</span>';
                };
                
                ws.onclose = function() {
                    log('🔌 WebSocket连接已关闭');
                };
            } catch (error) {
                log('❌ WebSocket测试失败: ' + error.message, 'error');
                document.getElementById('websocket-status').innerHTML = '<span class="error">❌ 测试失败</span>';
            }
        }
        
        // 测试本地存储
        function testStorage() {
            log('🔄 开始测试本地存储...');
            try {
                const testKey = 'quant_platform_test';
                const testValue = 'test_data_' + Date.now();
                
                localStorage.setItem(testKey, testValue);
                const retrieved = localStorage.getItem(testKey);
                
                if (retrieved === testValue) {
                    log('✅ 本地存储测试成功', 'success');
                    document.getElementById('storage-status').innerHTML = '<span class="success">✅ 功能正常</span>';
                    localStorage.removeItem(testKey);
                } else {
                    log('❌ 本地存储测试失败', 'error');
                    document.getElementById('storage-status').innerHTML = '<span class="error">❌ 功能异常</span>';
                }
            } catch (error) {
                log('❌ 本地存储错误: ' + error.message, 'error');
                document.getElementById('storage-status').innerHTML = '<span class="error">❌ 功能错误</span>';
            }
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            log('🚀 量化投资平台测试页面已加载', 'success');
            log('📊 开始自动检测系统状态...');
            
            // 延迟执行测试，避免页面加载冲突
            setTimeout(() => {
                testAPI();
                testStorage();
                // WebSocket测试可能需要后端服务，暂时注释
                // testWebSocket();
            }, 1000);
        });
    </script>
</body>
</html>
