#!/usr/bin/env python3
"""
快速数据库初始化脚本
创建基本的用户表和测试数据
"""

import sqlite3
import bcrypt
import os
from datetime import datetime

def hash_password(password: str) -> str:
    """哈希密码"""
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

def init_database():
    """初始化数据库"""
    print("🚀 开始初始化数据库...")
    
    # 确保数据库目录存在
    db_dir = "backend"
    if not os.path.exists(db_dir):
        os.makedirs(db_dir)
    
    db_path = os.path.join(db_dir, "app.db")
    
    # 如果数据库文件存在，先删除
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"🗑️  删除旧数据库文件: {db_path}")
    
    # 创建数据库连接
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 创建用户表
        cursor.execute("""
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                full_name TEXT,
                hashed_password TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                is_superuser BOOLEAN DEFAULT 0,
                is_verified BOOLEAN DEFAULT 1,
                role TEXT DEFAULT 'user',
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login_at TIMESTAMP
            )
        """)
        print("✅ 创建用户表")
        
        # 创建股票表
        cursor.execute("""
            CREATE TABLE stocks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                exchange TEXT NOT NULL,
                sector TEXT,
                industry TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ 创建股票表")
        
        # 创建订单表
        cursor.execute("""
            CREATE TABLE orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                order_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)
        print("✅ 创建订单表")
        
        # 创建持仓表
        cursor.execute("""
            CREATE TABLE positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                symbol TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                avg_cost REAL NOT NULL,
                current_price REAL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, symbol)
            )
        """)
        print("✅ 创建持仓表")
        
        # 插入测试用户
        users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'full_name': '系统管理员',
                'password': 'admin123',
                'is_superuser': 1,
                'role': 'admin'
            },
            {
                'username': 'demo',
                'email': '<EMAIL>', 
                'full_name': '演示用户',
                'password': 'demo123',
                'is_superuser': 0,
                'role': 'user'
            },
            {
                'username': 'test',
                'email': '<EMAIL>',
                'full_name': '测试用户',
                'password': 'test123',
                'is_superuser': 0,
                'role': 'user'
            }
        ]
        
        for user_data in users_data:
            hashed_password = hash_password(user_data['password'])
            cursor.execute("""
                INSERT INTO users (username, email, full_name, hashed_password, is_active, is_superuser, role)
                VALUES (?, ?, ?, ?, 1, ?, ?)
            """, (
                user_data['username'],
                user_data['email'],
                user_data['full_name'],
                hashed_password,
                user_data['is_superuser'],
                user_data['role']
            ))
            print(f"✅ 创建用户: {user_data['username']} / {user_data['password']}")
        
        # 插入示例股票数据
        stocks_data = [
            ("000001.SZ", "平安银行", "SZSE", "金融", "银行"),
            ("000002.SZ", "万科A", "SZSE", "房地产", "房地产开发"),
            ("600000.SH", "浦发银行", "SSE", "金融", "银行"),
            ("600036.SH", "招商银行", "SSE", "金融", "银行"),
            ("600519.SH", "贵州茅台", "SSE", "消费", "白酒"),
            ("000858.SZ", "五粮液", "SZSE", "消费", "白酒"),
            ("300015.SZ", "爱尔眼科", "SZSE", "医疗", "医疗服务"),
            ("002415.SZ", "海康威视", "SZSE", "科技", "安防设备"),
        ]
        
        for symbol, name, exchange, sector, industry in stocks_data:
            cursor.execute("""
                INSERT INTO stocks (symbol, name, exchange, sector, industry)
                VALUES (?, ?, ?, ?, ?)
            """, (symbol, name, exchange, sector, industry))
        
        print("✅ 插入示例股票数据")
        
        # 为demo用户创建示例持仓
        demo_positions = [
            ("000001.SZ", 1000, 12.50),
            ("600036.SH", 500, 45.20),
            ("600519.SH", 100, 1800.00)
        ]
        
        # 获取demo用户ID
        cursor.execute("SELECT id FROM users WHERE username = 'demo'")
        demo_user_id = cursor.fetchone()[0]
        
        for symbol, quantity, avg_cost in demo_positions:
            cursor.execute("""
                INSERT INTO positions (user_id, symbol, quantity, avg_cost, current_price)
                VALUES (?, ?, ?, ?, ?)
            """, (demo_user_id, symbol, quantity, avg_cost, avg_cost * 1.05))
        
        print("✅ 创建示例持仓数据")
        
        # 提交事务
        conn.commit()
        print("✅ 数据库初始化完成")
        
        # 显示创建的用户信息
        print("\n📋 创建的用户账户:")
        print("=" * 50)
        for user_data in users_data:
            print(f"用户名: {user_data['username']}")
            print(f"密码: {user_data['password']}")
            print(f"邮箱: {user_data['email']}")
            print(f"角色: {user_data['role']}")
            print("-" * 30)
        
        print(f"\n📁 数据库文件位置: {os.path.abspath(db_path)}")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    init_database()
