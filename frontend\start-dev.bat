@echo off
echo 🚀 启动量化投资平台Vue应用...

cd /d "%~dp0"

echo 📍 当前目录: %CD%
echo 🔧 清理端口占用...

:: 清理可能占用5173端口的进程
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5173') do (
    if not "%%a"=="0" (
        echo 终止进程 %%a
        taskkill /F /PID %%a >nul 2>&1
    )
)

echo ✅ 端口清理完成

echo 🔧 设置环境变量...
set NODE_ENV=development
set VITE_API_BASE_URL=http://localhost:8000
set VITE_WS_BASE_URL=ws://localhost:8000

echo 🚀 启动Vite开发服务器...
npx vite --config vite.config.dev.ts --port 5173 --host 0.0.0.0

pause
