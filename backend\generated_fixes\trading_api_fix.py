
# 修复后的交易API路由
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from datetime import datetime

from app.core.database import get_db
from app.core.dependencies import get_current_active_user
from app.db.models.user import User
from app.db.models.trading import Order, Position, Trade
from app.schemas.trading import (
    OrderRequest, OrderResponse, OrderListResponse,
    PositionResponse, AccountResponse
)
from app.services.trading_service import TradingService

router = APIRouter()

@router.post("/orders", response_model=OrderResponse)
async def create_order(
    order_data: OrderRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """创建订单"""
    service = TradingService(db)
    
    # 风险检查
    risk_check = await service.check_order_risk(current_user.id, order_data)
    if not risk_check.passed:
        raise HTTPException(status_code=400, detail=risk_check.message)
    
    # 创建订单
    order = await service.create_order(current_user.id, order_data)
    
    return OrderResponse(
        success=True,
        message="订单创建成功",
        data=order
    )

@router.get("/orders", response_model=OrderListResponse)
async def list_orders(
    status: Optional[str] = None,
    symbol: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取订单列表"""
    service = TradingService(db)
    orders = await service.get_user_orders(
        user_id=current_user.id,
        status=status,
        symbol=symbol,
        skip=skip,
        limit=limit
    )
    
    return OrderListResponse(
        success=True,
        data=orders,
        total=len(orders),
        skip=skip,
        limit=limit
    )

@router.delete("/orders/{order_id}")
async def cancel_order(
    order_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """取消订单"""
    service = TradingService(db)
    
    # 验证订单所有权
    order = await service.get_order(order_id)
    if not order or order.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 取消订单
    result = await service.cancel_order(order_id)
    
    return {"success": result, "message": "订单已取消" if result else "取消失败"}

@router.get("/positions", response_model=List[PositionResponse])
async def get_positions(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取持仓列表"""
    service = TradingService(db)
    positions = await service.get_user_positions(current_user.id)
    
    return positions

@router.get("/account", response_model=AccountResponse)
async def get_account_info(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取账户信息"""
    service = TradingService(db)
    account = await service.get_account_info(current_user.id)
    
    if not account:
        # 创建默认账户
        account = await service.create_default_account(current_user.id)
    
    return AccountResponse(
        success=True,
        data=account
    )
