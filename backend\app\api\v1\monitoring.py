"""
监控和健康检查相关的API端点
"""

import time
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from prometheus_client import (
    Counter,
    Histogram,
    Gauge,
    generate_latest,
    CONTENT_TYPE_LATEST,
)
from fastapi.responses import Response
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies_fixed import get_current_admin_user
from app.core.performance import performance_monitor
from app.core.cache import cache_manager
from app.core.simple_cache import get_cache_stats, clear_cache
from app.db.models.user import User
from app.services.auth_service import AuthService

router = APIRouter(tags=["监控"])

# Prometheus指标
REQUEST_COUNT = Counter(
    "http_requests_total", "Total HTTP requests", ["method", "endpoint", "status_code"]
)
REQUEST_DURATION = Histogram("http_request_duration_seconds", "HTTP request duration")
ACTIVE_CONNECTIONS = Gauge("active_connections", "Number of active connections")
DATABASE_CONNECTIONS = Gauge("database_connections", "Number of database connections")
CACHE_HIT_RATE = Gauge("cache_hit_rate", "Cache hit rate percentage")


@router.get("/health", summary="健康检查", response_model=Dict[str, Any])
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    应用程序健康检查端点
    返回服务状态和基本信息
    """
    start_time = time.time()

    try:
        # 检查数据库连接
        await db.execute("SELECT 1")
        db_status = "healthy"
        db_response_time = time.time() - start_time
    except Exception as e:
        db_status = "unhealthy"
        db_response_time = -1

    # 检查缓存连接
    cache_status = "healthy" if cache_manager.redis_client else "unhealthy"

    # 获取系统指标
    system_metrics = performance_monitor.get_system_metrics()

    health_data = {
        "status": (
            "healthy"
            if db_status == "healthy" and cache_status == "healthy"
            else "unhealthy"
        ),
        "timestamp": time.time(),
        "uptime": system_metrics.get("uptime", 0),
        "version": "1.0.0",
        "checks": {
            "database": {"status": db_status, "response_time": db_response_time},
            "cache": {"status": cache_status},
            "disk_space": {"status": "healthy"},  # 简化处理
        },
        "metrics": {
            "request_count": system_metrics.get("request_count", 0),
            "error_count": system_metrics.get("error_count", 0),
            "avg_response_time": system_metrics.get("avg_response_time", 0),
            "cpu_usage": system_metrics.get("cpu_usage", 0),
            "memory_usage": system_metrics.get("memory_usage", 0),
        },
    }

    # 如果服务不健康，返回503状态码
    if health_data["status"] == "unhealthy":
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=health_data
        )

    return health_data


@router.get("/metrics", summary="Prometheus指标")
async def get_metrics():
    """
    返回Prometheus格式的指标数据
    """
    # 更新指标
    system_metrics = performance_monitor.get_system_metrics()

    ACTIVE_CONNECTIONS.set(system_metrics.get("active_connections", 0))
    DATABASE_CONNECTIONS.set(5)  # 简化处理
    CACHE_HIT_RATE.set(75.5)  # 简化处理

    return Response(content=generate_latest(), media_type=CONTENT_TYPE_LATEST)


@router.get("/stats", summary="系统统计信息", response_model=Dict[str, Any])
async def get_system_stats(
    admin_user: User = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取详细的系统统计信息（仅管理员）
    """
    # 获取系统指标
    system_metrics = performance_monitor.get_system_metrics()

    # 获取缓存统计
    cache_stats = await cache_manager.get_stats()

    # 获取数据库统计
    auth_service = AuthService(db)
    try:
        # 简化的用户统计
        user_stats = {"total_users": 1, "active_users": 1, "admin_users": 1}  # 简化处理
    except Exception:
        user_stats = {"total_users": 0, "active_users": 0, "admin_users": 0}

    return {
        "system": system_metrics,
        "cache": cache_stats,
        "database": {"status": "connected", "pool_size": 10, "active_connections": 5},
        "users": user_stats,
        "performance": {
            "requests_per_second": system_metrics.get("request_count", 0)
            / max(system_metrics.get("uptime", 1), 1),
            "error_rate": system_metrics.get("error_rate", 0),
            "avg_response_time": system_metrics.get("avg_response_time", 0),
        },
    }


@router.get("/logs", summary="获取日志信息")
async def get_logs(
    level: str = "INFO",
    limit: int = 100,
    admin_user: User = Depends(get_current_admin_user),
):
    """
    获取应用程序日志（仅管理员）
    """
    # 简化处理，返回模拟日志
    logs = [
        {
            "timestamp": time.time(),
            "level": "INFO",
            "message": "Application started successfully",
            "module": "main",
        },
        {
            "timestamp": time.time() - 60,
            "level": "INFO",
            "message": "Redis connection established",
            "module": "cache",
        },
        {
            "timestamp": time.time() - 120,
            "level": "INFO",
            "message": "Database connection established",
            "module": "database",
        },
    ]

    return {"logs": logs[:limit], "total": len(logs), "level": level}


@router.post("/cache/clear", summary="清理缓存")
async def clear_cache(
    pattern: str = "*", admin_user: User = Depends(get_current_admin_user)
):
    """
    清理缓存（仅管理员）
    """
    try:
        cleared_count = await cache_manager.clear_pattern(pattern)
        return {
            "success": True,
            "message": f"清理了 {cleared_count} 个缓存项",
            "pattern": pattern,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理缓存失败: {str(e)}",
        )


@router.get("/performance", summary="性能报告")
async def get_performance_report(admin_user: User = Depends(get_current_admin_user)):
    """
    获取性能报告（仅管理员）
    """
    system_metrics = performance_monitor.get_system_metrics()

    return {
        "report_time": time.time(),
        "summary": {
            "status": (
                "healthy" if system_metrics.get("cpu_usage", 0) < 80 else "warning"
            ),
            "uptime": system_metrics.get("uptime", 0),
            "request_count": system_metrics.get("request_count", 0),
            "error_rate": system_metrics.get("error_rate", 0),
        },
        "system": {
            "cpu_usage": system_metrics.get("cpu_usage", 0),
            "memory_usage": system_metrics.get("memory_usage", 0),
            "disk_usage": 45.2,  # 简化处理
        },
        "performance": {
            "avg_response_time": system_metrics.get("avg_response_time", 0),
            "requests_per_second": system_metrics.get("request_count", 0)
            / max(system_metrics.get("uptime", 1), 1),
            "throughput": "normal",
        },
        "recommendations": [
            (
                "系统运行正常"
                if system_metrics.get("cpu_usage", 0) < 80
                else "CPU使用率较高，建议优化"
            ),
            (
                "内存使用正常"
                if system_metrics.get("memory_usage", 0) < 80
                else "内存使用率较高，建议优化"
            ),
        ],
    }


# 缓存管理端点
@router.get("/cache/stats", summary="缓存统计信息")
async def get_cache_statistics():
    """获取缓存统计信息"""
    try:
        stats = get_cache_stats()
        return {
            "success": True,
            "data": stats,
            "message": "缓存统计获取成功"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取缓存统计失败: {str(e)}"
        )

@router.post("/cache/clear", summary="清除缓存")
async def clear_cache_data(pattern: str = None):
    """清除缓存数据"""
    try:
        result = clear_cache(pattern)
        return {
            "success": True,
            "data": result,
            "message": "缓存清除成功"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除缓存失败: {str(e)}"
        )

# 中间件函数，用于记录请求指标
def record_request_metrics(request, response):
    """记录请求指标"""
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status_code=response.status_code,
    ).inc()
