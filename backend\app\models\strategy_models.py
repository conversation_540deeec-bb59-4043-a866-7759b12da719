"""
策略相关数据库模型
"""

import uuid
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum, ForeignKey, Index, Integer, 
    Numeric, String, Text
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


# 枚举定义
class StrategyStatus(str, Enum):
    DRAFT = "draft"
    TESTING = "testing"
    ACTIVE = "active"
    PAUSED = "paused"
    STOPPED = "stopped"
    ARCHIVED = "archived"


class StrategyType(str, Enum):
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    ARBITRAGE = "arbitrage"
    MARKET_MAKING = "market_making"
    HIGH_FREQUENCY = "high_frequency"
    QUANTITATIVE = "quantitative"
    CUSTOM = "custom"


class BacktestStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class Strategy(Base):
    """策略表"""
    __tablename__ = "strategies"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    
    # 策略基础信息
    name = Column(String(100), nullable=False, comment="策略名称")
    description = Column(Text, nullable=True, comment="策略描述")
    strategy_type = Column(SQLEnum(StrategyType), nullable=False, comment="策略类型")
    
    # 策略代码和配置
    code = Column(Text, nullable=True, comment="策略代码")
    config = Column(Text, nullable=True, comment="策略配置(JSON)")
    parameters = Column(Text, nullable=True, comment="策略参数(JSON)")
    
    # 状态信息
    status = Column(SQLEnum(StrategyStatus), default=StrategyStatus.DRAFT, 
                   nullable=False, index=True, comment="策略状态")
    is_active = Column(Boolean, default=False, comment="是否激活")
    is_public = Column(Boolean, default=False, comment="是否公开")
    
    # 风险管理
    max_position_size = Column(Numeric(15, 2), nullable=True, comment="最大持仓规模")
    max_daily_loss = Column(Numeric(15, 2), nullable=True, comment="最大日损失")
    max_drawdown = Column(Numeric(8, 4), nullable=True, comment="最大回撤")
    
    # 运行统计
    total_trades = Column(Integer, default=0, comment="总交易次数")
    winning_trades = Column(Integer, default=0, comment="盈利交易次数")
    losing_trades = Column(Integer, default=0, comment="亏损交易次数")
    total_pnl = Column(Numeric(15, 2), default=0, comment="总盈亏")
    win_rate = Column(Numeric(5, 4), default=0, comment="胜率")
    sharpe_ratio = Column(Numeric(8, 4), nullable=True, comment="夏普比率")
    max_drawdown_pct = Column(Numeric(8, 4), nullable=True, comment="最大回撤百分比")
    
    # 时间信息
    start_date = Column(DateTime, nullable=True, comment="策略开始时间")
    end_date = Column(DateTime, nullable=True, comment="策略结束时间")
    last_run_time = Column(DateTime, nullable=True, comment="最后运行时间")
    
    # 版本信息
    version = Column(String(20), default="1.0.0", comment="策略版本")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User", back_populates="strategies")
    orders = relationship("Order", back_populates="strategy")
    positions = relationship("Position", back_populates="strategy")
    backtests = relationship("Backtest", back_populates="strategy", cascade="all, delete-orphan")
    performance_records = relationship("StrategyPerformance", back_populates="strategy", 
                                     cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('idx_strategies_user_id', 'user_id'),
        Index('idx_strategies_status', 'status'),
        Index('idx_strategies_type', 'strategy_type'),
        Index('idx_strategies_active', 'is_active'),
        Index('idx_strategies_created_at', 'created_at'),
    )


class Backtest(Base):
    """回测表"""
    __tablename__ = "backtests"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    strategy_id = Column(UUID(as_uuid=True), ForeignKey('strategies.id'), nullable=False, index=True)
    
    # 回测基础信息
    name = Column(String(100), nullable=False, comment="回测名称")
    description = Column(Text, nullable=True, comment="回测描述")
    
    # 回测配置
    start_date = Column(DateTime, nullable=False, comment="回测开始时间")
    end_date = Column(DateTime, nullable=False, comment="回测结束时间")
    initial_capital = Column(Numeric(15, 2), nullable=False, comment="初始资金")
    benchmark = Column(String(20), nullable=True, comment="基准标的")
    
    # 状态信息
    status = Column(SQLEnum(BacktestStatus), default=BacktestStatus.PENDING, 
                   nullable=False, index=True, comment="回测状态")
    
    # 回测结果
    final_value = Column(Numeric(15, 2), nullable=True, comment="最终价值")
    total_return = Column(Numeric(8, 4), nullable=True, comment="总收益率")
    annual_return = Column(Numeric(8, 4), nullable=True, comment="年化收益率")
    max_drawdown = Column(Numeric(8, 4), nullable=True, comment="最大回撤")
    sharpe_ratio = Column(Numeric(8, 4), nullable=True, comment="夏普比率")
    sortino_ratio = Column(Numeric(8, 4), nullable=True, comment="索提诺比率")
    volatility = Column(Numeric(8, 4), nullable=True, comment="波动率")
    beta = Column(Numeric(8, 4), nullable=True, comment="贝塔值")
    alpha = Column(Numeric(8, 4), nullable=True, comment="阿尔法值")
    
    # 交易统计
    total_trades = Column(Integer, default=0, comment="总交易次数")
    winning_trades = Column(Integer, default=0, comment="盈利交易次数")
    losing_trades = Column(Integer, default=0, comment="亏损交易次数")
    win_rate = Column(Numeric(5, 4), default=0, comment="胜率")
    avg_win = Column(Numeric(15, 2), nullable=True, comment="平均盈利")
    avg_loss = Column(Numeric(15, 2), nullable=True, comment="平均亏损")
    profit_factor = Column(Numeric(8, 4), nullable=True, comment="盈利因子")
    
    # 时间信息
    start_time = Column(DateTime, nullable=True, comment="实际开始时间")
    end_time = Column(DateTime, nullable=True, comment="实际结束时间")
    duration = Column(Integer, nullable=True, comment="运行时长(秒)")
    
    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 结果数据
    result_data = Column(Text, nullable=True, comment="回测结果数据(JSON)")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), 
                       nullable=False, comment="更新时间")
    
    # 关系
    user = relationship("User")
    strategy = relationship("Strategy", back_populates="backtests")

    # 索引
    __table_args__ = (
        Index('idx_backtests_user_id', 'user_id'),
        Index('idx_backtests_strategy_id', 'strategy_id'),
        Index('idx_backtests_status', 'status'),
        Index('idx_backtests_created_at', 'created_at'),
        Index('idx_backtests_start_date', 'start_date'),
    )


class StrategyPerformance(Base):
    """策略表现记录表"""
    __tablename__ = "strategy_performance"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 关联信息
    strategy_id = Column(UUID(as_uuid=True), ForeignKey('strategies.id'), nullable=False, index=True)
    
    # 记录信息
    record_date = Column(DateTime, nullable=False, index=True, comment="记录日期")
    
    # 资金信息
    total_value = Column(Numeric(15, 2), nullable=False, comment="总价值")
    cash = Column(Numeric(15, 2), nullable=False, comment="现金")
    position_value = Column(Numeric(15, 2), nullable=False, comment="持仓价值")
    
    # 收益信息
    daily_return = Column(Numeric(8, 4), default=0, comment="日收益率")
    total_return = Column(Numeric(8, 4), default=0, comment="总收益率")
    benchmark_return = Column(Numeric(8, 4), nullable=True, comment="基准收益率")
    excess_return = Column(Numeric(8, 4), nullable=True, comment="超额收益率")
    
    # 风险指标
    volatility = Column(Numeric(8, 4), nullable=True, comment="波动率")
    drawdown = Column(Numeric(8, 4), nullable=True, comment="回撤")
    var_95 = Column(Numeric(15, 2), nullable=True, comment="95% VaR")
    
    # 交易统计
    daily_trades = Column(Integer, default=0, comment="当日交易次数")
    turnover = Column(Numeric(15, 2), default=0, comment="成交额")
    
    # 系统字段
    created_at = Column(DateTime, default=func.now(), nullable=False, comment="创建时间")
    
    # 关系
    strategy = relationship("Strategy", back_populates="performance_records")

    # 索引
    __table_args__ = (
        Index('idx_strategy_performance_strategy_id', 'strategy_id'),
        Index('idx_strategy_performance_record_date', 'record_date'),
        Index('idx_strategy_performance_strategy_date', 'strategy_id', 'record_date'),
    )