<template>
  <div class="simple-test">
    <div class="header">
      <h1>🧪 功能测试</h1>
      <button @click="goBack" class="back-btn">← 返回首页</button>
    </div>
    
    <div class="test-content">
      <div class="test-section">
        <h3>🔌 API连接测试</h3>
        <div class="test-grid">
          <div v-for="api in apiTests" :key="api.name" class="test-card">
            <div class="test-header">
              <span class="test-name">{{ api.name }}</span>
              <span class="test-status" :class="api.status">{{ api.statusText }}</span>
            </div>
            <div class="test-details">
              <div>端点: {{ api.endpoint }}</div>
              <div v-if="api.responseTime">响应时间: {{ api.responseTime }}ms</div>
              <div v-if="api.error" class="error-text">错误: {{ api.error }}</div>
            </div>
            <button @click="testSingleAPI(api)" class="test-btn" :disabled="api.testing">
              {{ api.testing ? '测试中...' : '重新测试' }}
            </button>
          </div>
        </div>
        
        <div class="test-actions">
          <button @click="testAllAPIs" class="action-btn primary" :disabled="isTestingAll">
            {{ isTestingAll ? '测试中...' : '🚀 测试所有API' }}
          </button>
          <button @click="clearResults" class="action-btn secondary">
            🗑️ 清除结果
          </button>
        </div>
      </div>
      
      <div class="test-section">
        <h3>🧭 路由测试</h3>
        <div class="route-tests">
          <div v-for="route in routeTests" :key="route.path" class="route-item">
            <span class="route-path">{{ route.path }}</span>
            <span class="route-name">{{ route.name }}</span>
            <button @click="testRoute(route)" class="route-btn">访问</button>
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>💾 本地存储测试</h3>
        <div class="storage-tests">
          <div class="storage-item">
            <label>测试数据:</label>
            <input v-model="testData" placeholder="输入测试数据" />
            <button @click="saveToStorage">保存</button>
            <button @click="loadFromStorage">加载</button>
            <button @click="clearStorage">清除</button>
          </div>
          <div class="storage-result">
            <strong>存储结果:</strong> {{ storageResult }}
          </div>
        </div>
      </div>
      
      <div class="test-section">
        <h3>📊 性能测试</h3>
        <div class="performance-tests">
          <div class="perf-item">
            <span>页面加载时间:</span>
            <span class="perf-value">{{ performanceData.loadTime }}ms</span>
          </div>
          <div class="perf-item">
            <span>DOM节点数量:</span>
            <span class="perf-value">{{ performanceData.domNodes }}</span>
          </div>
          <div class="perf-item">
            <span>内存使用:</span>
            <span class="perf-value">{{ performanceData.memoryUsage }}MB</span>
          </div>
          <button @click="runPerformanceTest" class="action-btn info">
            🔍 运行性能测试
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

interface APITest {
  name: string
  endpoint: string
  status: string
  statusText: string
  responseTime?: number
  error?: string
  testing: boolean
}

const apiTests = ref<APITest[]>([
  { name: '健康检查', endpoint: '/health', status: 'pending', statusText: '待测试', testing: false },
  { name: '市场数据', endpoint: '/api/v1/market/stocks', status: 'pending', statusText: '待测试', testing: false },
  { name: '交易接口', endpoint: '/api/v1/trading/orders', status: 'pending', statusText: '待测试', testing: false },
  { name: '策略接口', endpoint: '/api/v1/strategy/list', status: 'pending', statusText: '待测试', testing: false },
  { name: '用户接口', endpoint: '/api/v1/user/profile', status: 'pending', statusText: '待测试', testing: false }
])

const routeTests = ref([
  { path: '/', name: '首页' },
  { path: '/dashboard', name: '仪表盘' },
  { path: '/test', name: '测试页面' }
])

const isTestingAll = ref(false)
const testData = ref('')
const storageResult = ref('无')
const performanceData = ref({
  loadTime: 0,
  domNodes: 0,
  memoryUsage: 0
})

const goBack = () => {
  router.push('/')
}

const testSingleAPI = async (api: APITest) => {
  api.testing = true
  api.status = 'testing'
  api.statusText = '测试中...'
  
  const startTime = Date.now()
  
  try {
    const response = await fetch(`http://localhost:8000${api.endpoint}`)
    const endTime = Date.now()
    api.responseTime = endTime - startTime
    
    if (response.ok) {
      api.status = 'success'
      api.statusText = '成功'
      api.error = undefined
    } else {
      api.status = 'warning'
      api.statusText = `HTTP ${response.status}`
      api.error = `响应状态: ${response.status}`
    }
  } catch (error) {
    const endTime = Date.now()
    api.responseTime = endTime - startTime
    api.status = 'error'
    api.statusText = '失败'
    api.error = (error as Error).message
  }
  
  api.testing = false
}

const testAllAPIs = async () => {
  isTestingAll.value = true
  
  for (const api of apiTests.value) {
    await testSingleAPI(api)
    // 添加小延迟避免并发过多
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  isTestingAll.value = false
}

const clearResults = () => {
  apiTests.value.forEach(api => {
    api.status = 'pending'
    api.statusText = '待测试'
    api.responseTime = undefined
    api.error = undefined
    api.testing = false
  })
}

const testRoute = (route: any) => {
  router.push(route.path)
}

const saveToStorage = () => {
  try {
    localStorage.setItem('testData', testData.value)
    storageResult.value = '保存成功'
  } catch (error) {
    storageResult.value = '保存失败: ' + (error as Error).message
  }
}

const loadFromStorage = () => {
  try {
    const data = localStorage.getItem('testData')
    if (data) {
      testData.value = data
      storageResult.value = '加载成功'
    } else {
      storageResult.value = '无数据'
    }
  } catch (error) {
    storageResult.value = '加载失败: ' + (error as Error).message
  }
}

const clearStorage = () => {
  try {
    localStorage.removeItem('testData')
    testData.value = ''
    storageResult.value = '清除成功'
  } catch (error) {
    storageResult.value = '清除失败: ' + (error as Error).message
  }
}

const runPerformanceTest = () => {
  // 页面加载时间
  if (performance.getEntriesByType) {
    const navEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
    if (navEntries.length > 0) {
      const navEntry = navEntries[0]
      performanceData.value.loadTime = Math.round(navEntry.loadEventEnd - navEntry.navigationStart)
    }
  }
  
  // DOM节点数量
  performanceData.value.domNodes = document.querySelectorAll('*').length
  
  // 内存使用（如果支持）
  if ('memory' in performance) {
    const memory = (performance as any).memory
    performanceData.value.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024)
  }
}

onMounted(() => {
  console.log('🧪 测试页面已加载')
  runPerformanceTest()
})
</script>

<style scoped>
.simple-test {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  color: #2c3e50;
  margin: 0;
}

.back-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.back-btn:hover {
  background: #5a6268;
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.test-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.test-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  background: #f8f9fa;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.test-name {
  font-weight: 600;
  color: #2c3e50;
}

.test-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.test-status.pending { background: #e9ecef; color: #6c757d; }
.test-status.testing { background: #fff3cd; color: #856404; }
.test-status.success { background: #d4edda; color: #155724; }
.test-status.warning { background: #fff3cd; color: #856404; }
.test-status.error { background: #f8d7da; color: #721c24; }

.test-details {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 10px;
}

.test-details div {
  margin-bottom: 5px;
}

.error-text {
  color: #dc3545 !important;
}

.test-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.test-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-actions {
  display: flex;
  gap: 15px;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary { background: #007bff; color: white; }
.action-btn.secondary { background: #6c757d; color: white; }
.action-btn.info { background: #17a2b8; color: white; }

.action-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.route-tests {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.route-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.route-path {
  font-family: monospace;
  color: #007bff;
}

.route-btn {
  padding: 5px 10px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.storage-tests, .performance-tests {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.storage-item {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.storage-item input {
  flex: 1;
  min-width: 200px;
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.storage-item button {
  padding: 8px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.storage-result {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.perf-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.perf-value {
  font-family: monospace;
  font-weight: 600;
  color: #007bff;
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
  }
  
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  .test-actions {
    flex-direction: column;
  }
  
  .storage-item {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
