#!/usr/bin/env python3
"""
最小化后端服务用于测试登录功能
"""

import sqlite3
import bcrypt
import jwt
from datetime import datetime, timedelta
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import os

# 创建FastAPI应用
app = FastAPI(title="最小化后端服务", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str

# 数据库路径
DB_PATH = "backend/app.db"

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except Exception as e:
        print(f"密码验证错误: {e}")
        return False

def create_access_token(data: dict, expires_delta: timedelta = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=30)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def authenticate_user(username: str, password: str):
    """验证用户"""
    if not os.path.exists(DB_PATH):
        print(f"数据库文件不存在: {DB_PATH}")
        return None
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute(
            "SELECT id, username, email, full_name, hashed_password, role FROM users WHERE username = ?",
            (username,)
        )
        user = cursor.fetchone()
        
        if not user:
            print(f"用户不存在: {username}")
            return None
        
        user_id, username, email, full_name, hashed_password, role = user
        print(f"找到用户: {username}, 验证密码...")
        
        if not verify_password(password, hashed_password):
            print("密码验证失败")
            return None
        
        print("密码验证成功")
        return {
            "id": user_id,
            "username": username,
            "email": email,
            "full_name": full_name,
            "role": role
        }
    except Exception as e:
        print(f"数据库查询错误: {e}")
        return None
    finally:
        conn.close()

# API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "database_exists": os.path.exists(DB_PATH)
    }

@app.post("/api/v1/auth/login-json")
async def login_json(credentials: LoginRequest):
    """JSON格式登录"""
    print(f"收到登录请求: {credentials.username}")
    
    try:
        # 验证用户
        user = authenticate_user(credentials.username, credentials.password)
        if not user:
            print(f"用户验证失败: {credentials.username}")
            raise HTTPException(
                status_code=401,
                detail="用户名或密码错误"
            )
        
        print(f"用户验证成功: {user}")
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": str(user["id"]), "username": user["username"]},
            expires_delta=access_token_expires
        )
        
        # 构建响应
        response = {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": 1800,  # 30分钟
            "user": {
                "id": user["id"],
                "username": user["username"],
                "email": user["email"],
                "full_name": user["full_name"],
                "role": user["role"],
                "permissions": []
            }
        }
        
        print(f"登录成功，返回响应")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"登录处理错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )

@app.get("/api/v1/users/me")
async def get_current_user():
    """获取当前用户信息"""
    return {
        "id": 1,
        "username": "demo",
        "email": "<EMAIL>",
        "full_name": "演示用户",
        "role": "user"
    }

@app.get("/api/v1/market/stocks")
async def get_stocks():
    """获取股票列表"""
    return {
        "success": True,
        "data": {
            "items": [
                {
                    "symbol": "000001.SZ",
                    "name": "平安银行",
                    "current_price": 12.50,
                    "change": 0.15,
                    "change_percent": 1.22
                }
            ],
            "total": 1,
            "page": 1,
            "size": 20
        }
    }

if __name__ == "__main__":
    print("启动最小化后端服务...")
    print(f"数据库路径: {os.path.abspath(DB_PATH)}")
    print(f"数据库存在: {os.path.exists(DB_PATH)}")
    print("健康检查: http://localhost:8000/health")
    print("登录API: http://localhost:8000/api/v1/auth/login-json")
    print("测试用户: demo/demo123, admin/admin123, test/test123")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
