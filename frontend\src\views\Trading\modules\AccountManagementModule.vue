<template>
  <div class="account-management-module">
    <div class="module-header">
      <h2>账户管理</h2>
      <p class="module-description">管理您的交易账户、资金划转和账户信息</p>
    </div>

    <div class="account-content">
      <!-- 账户列表 -->
      <div class="account-list-section">
        <div class="section-header">
          <h3>我的账户</h3>
          <el-button type="primary" @click="showAddAccountDialog = true">
            <el-icon><Plus /></el-icon>
            添加账户
          </el-button>
        </div>

        <div class="account-cards">
          <div
            v-for="account in accounts"
            :key="account.id"
            class="account-card"
            :class="{ active: account.id === currentAccount.id }"
            @click="switchAccount(account)"
          >
            <div class="card-header">
              <div class="account-type">
                <el-tag :type="account.type === 'real' ? 'danger' : 'success'">
                  {{ account.type === 'real' ? '实盘账户' : '模拟账户' }}
                </el-tag>
                <el-tag v-if="account.id === currentAccount.id" type="info">当前</el-tag>
              </div>
              <el-dropdown @command="handleAccountAction">
                <el-button text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`edit-${account.id}`">编辑</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${account.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <div class="card-content">
              <h4>{{ account.name }}</h4>
              <div class="account-info">
                <div class="info-item">
                  <span class="label">账户ID:</span>
                  <span class="value">{{ account.id }}</span>
                </div>
                <div class="info-item">
                  <span class="label">状态:</span>
                  <el-tag :type="account.status === 'active' ? 'success' : 'warning'" size="small">
                    {{ account.status === 'active' ? '正常' : '异常' }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <div class="funds-info">
                <div class="fund-item">
                  <span class="label">可用资金</span>
                  <span class="amount">¥{{ formatMoney(account.availableFunds) }}</span>
                </div>
                <div class="fund-item">
                  <span class="label">总资产</span>
                  <span class="amount">¥{{ formatMoney(account.totalAssets) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资金划转 -->
      <div class="funds-transfer-section">
        <div class="section-header">
          <h3>资金划转</h3>
          <p class="section-description">银证转账、账户间转账</p>
        </div>

        <div class="transfer-content">
          <el-tabs v-model="transferActiveTab" type="card">
            <el-tab-pane label="银证转账" name="bank-stock">
              <div class="transfer-form">
                <el-form label-width="100px">
                  <el-form-item label="转账方向">
                    <el-radio-group v-model="bankTransferForm.direction">
                      <el-radio label="bank-to-stock">银行转证券</el-radio>
                      <el-radio label="stock-to-bank">证券转银行</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="转账金额">
                    <el-input-number
                      v-model="bankTransferForm.amount"
                      :min="1"
                      :max="1000000"
                      style="width: 100%"
                      placeholder="请输入转账金额"
                    />
                  </el-form-item>
                  <el-form-item label="银行账户">
                    <el-select v-model="bankTransferForm.bankAccount" style="width: 100%">
                      <el-option label="工商银行 ****1234" value="icbc_1234" />
                      <el-option label="建设银行 ****5678" value="ccb_5678" />
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleBankTransfer">确认转账</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
            <el-tab-pane label="账户间转账" name="account-transfer">
              <div class="transfer-form">
                <el-form label-width="100px">
                  <el-form-item label="转出账户">
                    <el-select v-model="accountTransferForm.fromAccount" style="width: 100%">
                      <el-option
                        v-for="account in accounts"
                        :key="account.id"
                        :label="account.name"
                        :value="account.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="转入账户">
                    <el-select v-model="accountTransferForm.toAccount" style="width: 100%">
                      <el-option
                        v-for="account in accounts"
                        :key="account.id"
                        :label="account.name"
                        :value="account.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="转账金额">
                    <el-input-number
                      v-model="accountTransferForm.amount"
                      :min="1"
                      :max="100000"
                      style="width: 100%"
                      placeholder="请输入转账金额"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleAccountTransfer">确认转账</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 账户详情 -->
      <div class="account-details-section">
        <div class="section-header">
          <h3>账户详情</h3>
        </div>

        <div class="details-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="账户名称">{{ currentAccount.name }}</el-descriptions-item>
            <el-descriptions-item label="账户类型">
              <el-tag :type="currentAccount.type === 'real' ? 'danger' : 'success'">
                {{ currentAccount.type === 'real' ? '实盘账户' : '模拟账户' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag :type="currentAccount.status === 'active' ? 'success' : 'warning'">
                {{ currentAccount.status === 'active' ? '正常' : '异常' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开户时间">2024-01-15</el-descriptions-item>
            <el-descriptions-item label="可用资金">
              <span class="amount-text">¥{{ formatMoney(currentAccount.availableFunds) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="总资产">
              <span class="amount-text">¥{{ formatMoney(currentAccount.totalAssets) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="持仓市值">
              <span class="amount-text">¥{{ formatMoney(currentAccount.totalAssets - currentAccount.availableFunds) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="今日盈亏">
              <span class="profit-loss positive">+¥1,234.56</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <!-- 添加账户对话框 -->
    <el-dialog
      v-model="showAddAccountDialog"
      title="添加账户"
      width="500px"
    >
      <el-form :model="newAccountForm" label-width="100px">
        <el-form-item label="账户类型">
          <el-radio-group v-model="newAccountForm.type">
            <el-radio label="simulation">模拟账户</el-radio>
            <el-radio label="real">实盘账户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="账户名称">
          <el-input v-model="newAccountForm.name" placeholder="请输入账户名称" />
        </el-form-item>
        <el-form-item v-if="newAccountForm.type === 'simulation'" label="初始资金">
          <el-input-number
            v-model="newAccountForm.initialFunds"
            :min="10000"
            :max="********"
            :step="10000"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddAccountDialog = false">取消</el-button>
        <el-button type="primary" @click="addAccount">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled } from '@element-plus/icons-vue'


// Props
interface Props {
  accounts: Array<{
    id: string
    type: string
    name: string
    availableFunds: number
    totalAssets: number
    status: string
  }>
  currentAccount: {
    id: string
    type: string
    name: string
    availableFunds: number
    totalAssets: number
    status: string
  }
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  accountChanged: [account: any]
  fundsTransfer: [transferData: any]
}>()

// 响应式数据
const transferActiveTab = ref('bank-stock')
const showAddAccountDialog = ref(false)
const showEditDialog = ref(false)
const editingAccount = ref<any>(null)
const accounts = ref<any[]>([
  {
    id: '1',
    name: '模拟账户1',
    type: 'simulation',
    balance: 100000,
    available: 95000,
    frozen: 5000,
    status: 'active'
  }
])

const newAccountForm = reactive({
  type: 'simulation',
  name: '',
  initialFunds: 100000
})

const bankTransferForm = reactive({
  direction: 'bank-to-stock',
  amount: 0,
  bankAccount: 'icbc_1234'
})

const accountTransferForm = reactive({
  fromAccount: '',
  toAccount: '',
  amount: 0
})

// 计算属性
const formatMoney = computed(() => {
  return (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }
})

// 方法
const switchAccount = (account: any) => {
  emit('accountChanged', account)
}

const handleAccountAction = (command: string) => {
  const [action, accountId] = command.split('-')

  if (action === 'edit') {
    // 打开编辑账户对话框
    const account = accounts.value.find(acc => acc.id === accountId)
    if (account) {
      editingAccount.value = { ...account }
      showEditDialog.value = true
    }
  } else if (action === 'delete') {
    ElMessageBox.confirm(
      '确定要删除这个账户吗？此操作不可恢复。',
      '删除账户',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      ElMessage.success('账户删除成功')
    }).catch(() => {
      ElMessage.info('已取消删除')
    })
  }
}

const addAccount = () => {
  if (!newAccountForm.name.trim()) {
    ElMessage.warning('请输入账户名称')
    return
  }

  const newAccount = {
    id: `${newAccountForm.type}_${Date.now()}`,
    type: newAccountForm.type,
    name: newAccountForm.name,
    availableFunds: newAccountForm.type === 'simulation' ? newAccountForm.initialFunds : 0,
    totalAssets: newAccountForm.type === 'simulation' ? newAccountForm.initialFunds : 0,
    status: 'active'
  }

  // 这里应该调用API添加账户
  ElMessage.success('账户添加成功')
  showAddAccountDialog.value = false

  // 重置表单
  Object.assign(newAccountForm, {
    type: 'simulation',
    name: '',
    initialFunds: 100000
  })
}

const handleTransferSuccess = (transferData: any) => {
  emit('fundsTransfer', transferData)
}

const handleBankTransfer = () => {
  if (!bankTransferForm.amount || bankTransferForm.amount <= 0) {
    ElMessage.warning('请输入有效的转账金额')
    return
  }

  const transferData = {
    type: bankTransferForm.direction,
    amount: bankTransferForm.amount,
    bankAccount: bankTransferForm.bankAccount,
    timestamp: new Date().toISOString()
  }

  handleTransferSuccess(transferData)

  // 重置表单
  bankTransferForm.amount = 0
}

const handleAccountTransfer = () => {
  if (!accountTransferForm.fromAccount || !accountTransferForm.toAccount) {
    ElMessage.warning('请选择转出和转入账户')
    return
  }

  if (accountTransferForm.fromAccount === accountTransferForm.toAccount) {
    ElMessage.warning('转出和转入账户不能相同')
    return
  }

  if (!accountTransferForm.amount || accountTransferForm.amount <= 0) {
    ElMessage.warning('请输入有效的转账金额')
    return
  }

  const transferData = {
    type: 'account-transfer',
    fromAccount: accountTransferForm.fromAccount,
    toAccount: accountTransferForm.toAccount,
    amount: accountTransferForm.amount,
    timestamp: new Date().toISOString()
  }

  handleTransferSuccess(transferData)

  // 重置表单
  accountTransferForm.amount = 0
}
</script>

<style scoped lang="scss">
.account-management-module {
  height: 100%;
  overflow-y: auto;
}

.module-header {
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #303133;
  }

  .module-description {
    margin: 0;
    color: #606266;
    font-size: 14px;
  }
}

.account-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }

  .section-description {
    margin: 4px 0 0 0;
    color: #909399;
    font-size: 12px;
  }
}

.account-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.account-card {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }

  &.active {
    border-color: #409eff;
    background: #f0f9ff;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .account-type {
      display: flex;
      gap: 8px;
    }
  }

  .card-content {
    margin-bottom: 16px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #303133;
    }

    .account-info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        .label {
          color: #909399;
        }

        .value {
          color: #606266;
          font-family: monospace;
        }
      }
    }
  }

  .card-footer {
    .funds-info {
      display: flex;
      justify-content: space-between;

      .fund-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }

        .amount {
          font-size: 16px;
          font-weight: 600;
          color: #67c23a;
        }
      }
    }
  }
}

.transfer-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.transfer-form {
  max-width: 500px;
  margin: 0 auto;

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-button {
    width: 100%;
    margin-top: 16px;
  }
}

.details-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .amount-text {
    font-weight: 600;
    color: #67c23a;
  }

  .profit-loss {
    font-weight: 600;

    &.positive {
      color: #f56c6c;
    }

    &.negative {
      color: #67c23a;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .account-cards {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
