#!/usr/bin/env python3
"""
简单的量化投资平台测试
使用HTTP请求测试平台的基本功能
"""

import requests
import json
import time
from datetime import datetime
from urllib.parse import urljoin

class SimplePlatformTest:
    def __init__(self):
        self.base_url = "http://localhost:5173"
        self.backend_url = "http://localhost:8000"
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'frontend_status': 'unknown',
            'backend_status': 'unknown',
            'pages_tested': [],
            'issues_found': [],
            'recommendations': []
        }
        
    def test_frontend_access(self):
        """测试前端访问"""
        print("🌐 测试前端访问...")
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.test_results['frontend_status'] = 'accessible'
                content = response.text
                
                # 检查关键内容
                issues = []
                if '量化投资平台' not in content:
                    issues.append("页面标题缺失")
                if 'router-view' not in content and 'Vue' not in content:
                    issues.append("Vue应用可能未正确加载")
                if len(content) < 1000:
                    issues.append("页面内容过少，可能是空白页面")
                    
                if issues:
                    self.test_results['issues_found'].extend([
                        {'type': 'frontend_content', 'issues': issues}
                    ])
                    
                print(f"✅ 前端可访问 (状态码: {response.status_code})")
                print(f"📄 页面大小: {len(content)} 字符")
                
            else:
                self.test_results['frontend_status'] = 'error'
                self.test_results['issues_found'].append({
                    'type': 'frontend_access',
                    'status_code': response.status_code
                })
                print(f"❌ 前端访问失败 (状态码: {response.status_code})")
                
        except Exception as e:
            self.test_results['frontend_status'] = 'unreachable'
            self.test_results['issues_found'].append({
                'type': 'frontend_connection',
                'error': str(e)
            })
            print(f"❌ 前端连接失败: {e}")
            
    def test_backend_access(self):
        """测试后端访问"""
        print("🔧 测试后端访问...")
        try:
            # 测试健康检查
            response = requests.get(f"{self.backend_url}/health", timeout=10)
            if response.status_code == 200:
                self.test_results['backend_status'] = 'accessible'
                print(f"✅ 后端健康检查通过")
                
                # 测试API端点
                api_endpoints = [
                    "/api/v1/monitoring/system",
                    "/api/v1/storage/stats",
                    "/api/v1/debug/routes"
                ]
                
                for endpoint in api_endpoints:
                    try:
                        api_response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                        if api_response.status_code == 200:
                            print(f"✅ API端点 {endpoint} 正常")
                        else:
                            print(f"⚠️ API端点 {endpoint} 返回状态码: {api_response.status_code}")
                    except Exception as e:
                        print(f"❌ API端点 {endpoint} 测试失败: {e}")
                        
            else:
                self.test_results['backend_status'] = 'error'
                print(f"❌ 后端健康检查失败 (状态码: {response.status_code})")
                
        except Exception as e:
            self.test_results['backend_status'] = 'unreachable'
            print(f"❌ 后端连接失败: {e}")
            
    def analyze_current_issues(self):
        """分析当前问题"""
        print("🔍 分析当前问题...")
        
        # 基于测试结果分析问题
        if self.test_results['frontend_status'] == 'unreachable':
            self.test_results['recommendations'].append(
                "前端服务器未运行，请检查开发服务器状态"
            )
        elif self.test_results['frontend_status'] == 'error':
            self.test_results['recommendations'].append(
                "前端服务器返回错误，请检查应用配置"
            )
            
        if self.test_results['backend_status'] == 'unreachable':
            self.test_results['recommendations'].append(
                "后端服务器未运行，请启动后端服务"
            )
            
        # 检查布局问题
        if any('页面标题缺失' in str(issue) for issue in self.test_results['issues_found']):
            self.test_results['recommendations'].append(
                "页面标题缺失，可能是ProfessionalLayout组件被删除"
            )
            
    def generate_report(self):
        """生成测试报告"""
        timestamp = int(time.time())
        report_file = f"simple_test_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            
        print(f"\n📊 测试报告已生成: {report_file}")
        
        # 打印摘要
        print("\n" + "="*50)
        print("🎯 测试摘要")
        print("="*50)
        print(f"前端状态: {self.test_results['frontend_status']}")
        print(f"后端状态: {self.test_results['backend_status']}")
        print(f"发现问题: {len(self.test_results['issues_found'])} 个")
        print(f"改进建议: {len(self.test_results['recommendations'])} 条")
        
        if self.test_results['issues_found']:
            print("\n🚨 发现的问题:")
            for i, issue in enumerate(self.test_results['issues_found'], 1):
                print(f"  {i}. {issue}")
                
        if self.test_results['recommendations']:
            print("\n💡 改进建议:")
            for i, rec in enumerate(self.test_results['recommendations'], 1):
                print(f"  {i}. {rec}")
                
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始量化投资平台简单测试")
        print("="*50)
        
        self.test_frontend_access()
        self.test_backend_access()
        self.analyze_current_issues()
        self.generate_report()
        
        print("\n✅ 测试完成!")

if __name__ == "__main__":
    tester = SimplePlatformTest()
    tester.run_test()
