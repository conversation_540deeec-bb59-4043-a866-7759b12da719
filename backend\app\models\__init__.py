"""
统一数据库模型导入
"""

# 导入所有模型以确保 Alembic 能够检测到它们
from .user_models import (
    User, UserProfile, UserSettings, Role, Permission, UserRole,
    user_roles, role_permissions,
    UserRoleEnum, UserStatusEnum
)

from .trading_models import (
    Order, Trade, Position, Account, AccountHistory, AssetRecord,
    OrderStatus, OrderType, OrderSide, TradeType, PositionSide, AccountStatus
)

from .strategy_models import (
    Strategy, Backtest, StrategyPerformance,
    StrategyStatus, StrategyType, BacktestStatus
)

from .market_data_models import (
    Instrument, MarketKline, MarketTick, MarketDepth,
    DataFrequency, MarketDataType, InstrumentType
)

from .ctp_models import (
    CTPOrder, CTPTrade, CTPPosition, CTPAccount,
    CTPOrderStatus, CTPDirection, CTPOffsetFlag
)

# 导出所有模型
__all__ = [
    # 用户相关模型
    "User",
    "UserProfile", 
    "UserSettings",
    "Role",
    "Permission",
    "UserRole",
    "user_roles",
    "role_permissions",
    "UserRoleEnum",
    "UserStatusEnum",
    
    # 交易相关模型
    "Order",
    "Trade", 
    "Position",
    "Account",
    "AccountHistory",
    "AssetRecord",
    "OrderStatus",
    "OrderType", 
    "OrderSide",
    "TradeType",
    "PositionSide",
    "AccountStatus",
    
    # 策略相关模型
    "Strategy",
    "Backtest",
    "StrategyPerformance",
    "StrategyStatus",
    "StrategyType",
    "BacktestStatus",
    
    # 市场数据模型
    "Instrument",
    "MarketKline",
    "MarketTick",
    "MarketDepth",
    "DataFrequency",
    "MarketDataType",
    "InstrumentType",
    
    # CTP相关模型
    "CTPOrder",
    "CTPTrade",
    "CTPPosition",
    "CTPAccount",
    "CTPOrderStatus",
    "CTPDirection", 
    "CTPOffsetFlag",
]
